{% extends "base.html" %}

{% block title %}登录 - TweetAnalyst{% endblock %}

{% block extra_css %}
<style>
    body {
        background-color: #f8f9fa;
    }

    .login-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 2rem 1rem;
    }

    .login-card {
        max-width: 420px;
        width: 100%;
        border-radius: 1rem;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .login-card:hover {
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        transform: translateY(-5px);
    }

    .login-header {
        padding: 1.5rem;
        text-align: center;
        background: linear-gradient(135deg, #0d6efd, #0a58ca);
    }

    .login-logo {
        width: 80px;
        height: 80px;
        margin-bottom: 1rem;
        padding: 1rem;
        background-color: white;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    .login-logo i {
        font-size: 2.5rem;
        color: #0d6efd;
    }

    .login-title {
        color: white;
        margin-bottom: 0.5rem;
        font-weight: 600;
    }

    .login-subtitle {
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.9rem;
    }

    .login-body {
        padding: 2rem;
        background-color: white;
    }

    .form-floating {
        margin-bottom: 1.5rem;
    }

    .form-floating .form-control {
        padding-left: 2.5rem;
        height: calc(3.5rem + 2px);
        border-radius: 0.5rem;
    }

    .form-floating label {
        padding-left: 2.5rem;
    }

    .input-icon {
        position: absolute;
        left: 1rem;
        top: 1.25rem;
        color: #6c757d;
        z-index: 5;
    }

    .password-toggle {
        position: absolute;
        right: 1rem;
        top: 1.25rem;
        color: #6c757d;
        z-index: 5;
        cursor: pointer;
        background: none;
        border: none;
        padding: 0;
    }

    .login-btn {
        padding: 0.8rem 1.5rem;
        font-weight: 500;
        border-radius: 0.5rem;
        box-shadow: 0 4px 6px rgba(13, 110, 253, 0.25);
        transition: all 0.3s ease;
    }

    .login-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 8px rgba(13, 110, 253, 0.3);
    }

    .login-btn:active {
        transform: translateY(0);
    }

    .login-footer {
        text-align: center;
        padding: 1.5rem;
        background-color: #f8f9fa;
        border-top: 1px solid #e9ecef;
    }

    .login-footer p {
        margin-bottom: 0;
        color: #6c757d;
        font-size: 0.9rem;
    }

    .login-version {
        font-size: 0.8rem;
        color: #adb5bd;
    }

    .shake-animation {
        animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
    }

    @keyframes shake {
        10%, 90% { transform: translate3d(-1px, 0, 0); }
        20%, 80% { transform: translate3d(2px, 0, 0); }
        30%, 50%, 70% { transform: translate3d(-4px, 0, 0); }
        40%, 60% { transform: translate3d(4px, 0, 0); }
    }

    .fade-in {
        animation: fadeIn 0.5s ease-out forwards;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* 响应式调整 */
    @media (max-width: 576px) {
        .login-header {
            padding: 1.25rem;
        }

        .login-logo {
            width: 70px;
            height: 70px;
        }

        .login-body {
            padding: 1.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-card fade-in">
        <div class="login-header">
            <div class="login-logo">
                <i class="bi bi-twitter"></i>
            </div>
            <h2 class="login-title">TweetAnalyst</h2>
            <p class="login-subtitle">社交媒体内容分析系统</p>
        </div>

        <div class="login-body">
            <form method="post" id="login-form" autocomplete="off" novalidate>
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                {% if error %}
                <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        <div>{{ error }}</div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="关闭"></button>
                </div>
                {% endif %}

                <div class="form-floating position-relative">
                    <i class="bi bi-person-fill input-icon"></i>
                    <input type="text" class="form-control" id="username" name="username"
                           required autofocus placeholder="请输入用户名"
                           aria-label="用户名" aria-describedby="username-help">
                    <label for="username">用户名</label>
                    <div id="username-help" class="form-text">请输入您的管理员用户名</div>
                </div>

                <div class="form-floating position-relative">
                    <i class="bi bi-key-fill input-icon"></i>
                    <input type="password" class="form-control" id="password" name="password"
                           required placeholder="请输入密码"
                           aria-label="密码">
                    <label for="password">密码</label>
                    <button type="button" class="password-toggle" id="toggle-password"
                            aria-label="显示/隐藏密码">
                        <i class="bi bi-eye"></i>
                    </button>
                </div>

                <div class="form-check mb-4">
                    <input class="form-check-input" type="checkbox" id="remember-me" name="remember">
                    <label class="form-check-label" for="remember-me">
                        记住我
                    </label>
                </div>

                <div class="d-grid">
                    <button type="submit" class="btn btn-primary login-btn" id="login-btn">
                        <i class="bi bi-box-arrow-in-right me-2"></i>登录
                        <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                    </button>
                </div>
            </form>
        </div>

        <div class="login-footer">
            <p>© 2023-2025 TweetAnalyst</p>
            <span class="login-version">版本 1.5.0</span>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化表单验证
    initFormValidation();

    // 初始化密码显示/隐藏功能
    initPasswordToggle();

    // 初始化登录按钮动画
    initLoginButtonAnimation();

    // 初始化记住我功能
    initRememberMe();

    // 检查是否有错误消息，如果有则添加抖动动画
    checkForErrors();
});

// 初始化表单验证
function initFormValidation() {
    const loginForm = document.getElementById('login-form');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    const loginBtn = document.getElementById('login-btn');

    if (loginForm && usernameInput && passwordInput && loginBtn) {
        // 表单提交事件
        loginForm.addEventListener('submit', function(e) {
            let isValid = true;

            // 验证用户名
            if (!usernameInput.value.trim()) {
                isValid = false;
                showInputError(usernameInput, '请输入用户名');
            } else {
                clearInputError(usernameInput);
            }

            // 验证密码
            if (!passwordInput.value.trim()) {
                isValid = false;
                showInputError(passwordInput, '请输入密码');
            } else {
                clearInputError(passwordInput);
            }

            // 如果验证失败，阻止表单提交
            if (!isValid) {
                e.preventDefault();

                // 添加抖动动画
                const loginCard = document.querySelector('.login-card');
                if (loginCard) {
                    loginCard.classList.add('shake-animation');

                    // 动画结束后移除类
                    setTimeout(() => {
                        loginCard.classList.remove('shake-animation');
                    }, 500);
                }

                return false;
            }

            // 显示加载状态
            showLoading(loginBtn);

            // 允许表单提交
            return true;
        });

        // 输入框获取焦点时清除错误
        usernameInput.addEventListener('focus', function() {
            clearInputError(this);
        });

        passwordInput.addEventListener('focus', function() {
            clearInputError(this);
        });
    }
}

// 显示输入框错误
function showInputError(input, message) {
    input.classList.add('is-invalid');

    // 检查是否已存在错误消息
    let errorDiv = input.parentNode.querySelector('.invalid-feedback');

    if (!errorDiv) {
        // 创建错误消息元素
        errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        input.parentNode.appendChild(errorDiv);
    }

    // 设置错误消息
    errorDiv.textContent = message;
}

// 清除输入框错误
function clearInputError(input) {
    input.classList.remove('is-invalid');

    // 移除错误消息
    const errorDiv = input.parentNode.querySelector('.invalid-feedback');
    if (errorDiv) {
        errorDiv.remove();
    }
}

// 显示加载状态
function showLoading(button) {
    const spinner = button.querySelector('.spinner-border');
    const icon = button.querySelector('.bi');

    if (spinner && icon) {
        // 隐藏图标，显示加载动画
        icon.style.display = 'none';
        spinner.classList.remove('d-none');
    }

    // 禁用按钮
    button.disabled = true;
    button.textContent = '登录中...';

    // 添加加载图标
    if (spinner) {
        button.prepend(spinner);
    }
}

// 初始化密码显示/隐藏功能
function initPasswordToggle() {
    const togglePassword = document.getElementById('toggle-password');
    const passwordInput = document.getElementById('password');

    if (togglePassword && passwordInput) {
        togglePassword.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);

            // 切换图标
            const icon = this.querySelector('i');
            if (type === 'password') {
                icon.classList.remove('bi-eye-slash');
                icon.classList.add('bi-eye');
            } else {
                icon.classList.remove('bi-eye');
                icon.classList.add('bi-eye-slash');
            }
        });
    }
}

// 初始化登录按钮动画
function initLoginButtonAnimation() {
    const loginBtn = document.getElementById('login-btn');

    if (loginBtn) {
        // 鼠标悬停效果
        loginBtn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        loginBtn.addEventListener('mouseleave', function() {
            this.style.transform = '';
        });

        // 点击效果
        loginBtn.addEventListener('mousedown', function() {
            this.style.transform = 'translateY(0)';
        });

        loginBtn.addEventListener('mouseup', function() {
            this.style.transform = 'translateY(-2px)';
        });
    }
}

// 初始化记住我功能
function initRememberMe() {
    const rememberCheckbox = document.getElementById('remember-me');

    if (rememberCheckbox) {
        // 从本地存储中获取记住我状态
        const remembered = localStorage.getItem('remember-me') === 'true';

        // 设置复选框状态
        rememberCheckbox.checked = remembered;

        // 监听复选框变化
        rememberCheckbox.addEventListener('change', function() {
            localStorage.setItem('remember-me', this.checked);

            // 如果选中，则保存用户名
            const usernameInput = document.getElementById('username');
            if (usernameInput) {
                if (this.checked) {
                    localStorage.setItem('remembered-username', usernameInput.value);
                } else {
                    localStorage.removeItem('remembered-username');
                }
            }
        });

        // 如果记住我被选中，则填充用户名
        if (remembered) {
            const rememberedUsername = localStorage.getItem('remembered-username');
            const usernameInput = document.getElementById('username');

            if (rememberedUsername && usernameInput) {
                usernameInput.value = rememberedUsername;

                // 将焦点设置到密码输入框
                const passwordInput = document.getElementById('password');
                if (passwordInput) {
                    setTimeout(() => {
                        passwordInput.focus();
                    }, 100);
                }
            }
        }
    }
}

// 检查是否有错误消息
function checkForErrors() {
    const errorAlert = document.querySelector('.alert-danger');

    if (errorAlert) {
        // 添加抖动动画
        const loginCard = document.querySelector('.login-card');
        if (loginCard) {
            loginCard.classList.add('shake-animation');

            // 动画结束后移除类
            setTimeout(() => {
                loginCard.classList.remove('shake-animation');
            }, 500);
        }
    }
}
</script>
{% endblock %}
