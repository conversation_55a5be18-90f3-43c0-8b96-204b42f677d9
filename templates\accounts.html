{% extends "base.html" %}

{% block title %}账号管理 - TweetAnalyst{% endblock %}

{% block extra_css %}
<style>
    /* 禁用模态框动画 */
    .modal {
        transition: none !important;
        animation: none !important;
        -webkit-animation: none !important;
    }
    .modal.fade {
        opacity: 1 !important;
        transition: none !important;
        animation: none !important;
        -webkit-animation: none !important;
    }
    .modal-dialog {
        transition: none !important;
        transform: none !important;
        animation: none !important;
        -webkit-animation: none !important;
        margin: 1.75rem auto !important;
    }
    .modal-content {
        transition: none !important;
        animation: none !important;
        -webkit-animation: none !important;
    }
    .modal-backdrop {
        transition: none !important;
        animation: none !important;
        -webkit-animation: none !important;
    }
    .modal-backdrop.fade {
        opacity: 0.5 !important;
    }
    .modal.show .modal-dialog {
        transform: none !important;
    }

    /* 修复模态框鼠标事件问题 */
    .modal-open {
        overflow: hidden;
        padding-right: 0 !important;
    }
    .modal-open .modal {
        overflow-x: hidden;
        overflow-y: auto;
    }

    /* 账号详情卡片样式 */
    .account-profile-card {
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        background-color: var(--bs-card-bg);
    }

    .account-header {
        position: relative;
    }

    .account-banner {
        height: 150px;
        background-color: #1DA1F2;
    }

    .account-avatar-container {
        position: absolute;
        bottom: -40px;
        left: 20px;
    }

    .account-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        border: 4px solid var(--bs-card-bg);
        object-fit: cover;
    }

    .account-avatar-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        border: 4px solid var(--bs-card-bg);
        background-color: #1DA1F2;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .account-info {
        padding: 50px 20px 20px;
    }

    .account-display-name {
        font-weight: bold;
        margin-bottom: 0;
    }

    .account-username {
        color: var(--bs-secondary);
        margin-bottom: 1rem;
    }

    .account-bio {
        margin-bottom: 1rem;
    }

    .account-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-bottom: 1rem;
        color: var(--bs-secondary);
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .account-stats {
        display: flex;
        gap: 1.5rem;
        border-top: 1px solid var(--bs-border-color);
        padding-top: 1rem;
    }

    .stat-value {
        font-weight: bold;
    }

    .stat-label {
        color: var(--bs-secondary);
        margin-left: 0.25rem;
    }

    /* 新增样式 */
    .account-info-cell {
        min-width: 200px;
        padding: 0.75rem !important;
    }

    .platform-row {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
    }

    .user-info-row {
        padding: 0.25rem 0;
        margin-bottom: 0.5rem;
    }

    .user-name-info {
        flex: 1;
        min-width: 0;
    }

    .account-id-row {
        font-size: 0.875rem;
    }

    .account-row td {
        padding: 1rem 0.75rem;
        vertical-align: top;
    }

    .account-row:hover {
        background-color: #f8f9fa;
    }

    .verified-badge {
        color: #1da1f2;
        font-size: 0.875rem;
    }

    .followers-count {
        font-size: 0.75rem;
        color: #6c757d;
    }

    .bio-text {
        font-size: 0.75rem;
        color: #6c757d;
        line-height: 1.2;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .account-checkbox {
        margin: 0;
    }

    .account-row.selected {
        background-color: #e3f2fd !important;
    }

    .bulk-actions {
        background: linear-gradient(135deg, #17a2b8, #138496);
        color: white;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .stats-card {
        transition: transform 0.2s ease;
    }

    .stats-card:hover {
        transform: translateY(-2px);
    }

    .import-modal .modal-body {
        max-height: 60vh;
        overflow-y: auto;
    }

    .file-drop-zone {
        border: 2px dashed #dee2e6;
        border-radius: 0.5rem;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .file-drop-zone:hover,
    .file-drop-zone.dragover {
        border-color: #007bff;
        background-color: #f8f9fa;
    }

    .file-drop-zone.dragover {
        border-color: #28a745;
        background-color: #d4edda;
    }

    .preview-table {
        max-height: 300px;
        overflow-y: auto;
    }

    .import-progress {
        display: none;
    }

    .import-progress.show {
        display: block;
    }




</style>
{% endblock %}

{% block content %}
<div class="row mb-3">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">首页</a></li>
                <li class="breadcrumb-item active" aria-current="page">账号管理</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="border-bottom pb-2">
                <i class="bi bi-people-fill me-2 text-primary"></i>社交媒体账号管理
            </h2>
            <div class="btn-group">
                <button id="run-all-accounts-btn" class="btn btn-success me-2">
                    <i class="bi bi-play-fill me-1"></i> <span class="btn-text">立即抓取所有账号</span>
                    <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                </button>
                <a href="{{ url_for('add_account') }}" class="btn btn-primary">
                    <i class="bi bi-plus-lg me-1"></i> 添加账号
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 搜索和筛选控制 -->
<div class="row mb-3">
    <div class="col-md-6">
        <div class="input-group">
            <span class="input-group-text"><i class="bi bi-search"></i></span>
            <input type="text" class="form-control" id="global-search" placeholder="搜索账号ID、显示名称或标签...">
            <button class="btn btn-outline-secondary" type="button" id="clear-search-btn">
                <i class="bi bi-x"></i>
            </button>
        </div>
    </div>
    <div class="col-md-3">
        <div class="btn-group w-100">
            <button class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="bi bi-funnel me-1"></i>快速筛选
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" data-filter="verified"><i class="bi bi-patch-check me-2"></i>已验证账号</a></li>
                <li><a class="dropdown-item" href="#" data-filter="auto-reply"><i class="bi bi-chat-dots me-2"></i>启用自动回复</a></li>
                <li><a class="dropdown-item" href="#" data-filter="ai-bypass"><i class="bi bi-lightning me-2"></i>直接推送</a></li>
                <li><a class="dropdown-item" href="#" data-filter="twitter"><i class="bi bi-twitter me-2"></i>Twitter账号</a></li>
                <li><a class="dropdown-item" href="#" data-filter="weibo"><i class="bi bi-sina-weibo me-2"></i>微博账号</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="#" data-filter="clear"><i class="bi bi-arrow-clockwise me-2"></i>清除筛选</a></li>
            </ul>
        </div>
    </div>
    <div class="col-md-3">
        <div class="btn-group w-100">
            <button class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                <i class="bi bi-download me-1"></i>导入/导出
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" id="export-accounts-btn">
                    <i class="bi bi-file-earmark-excel me-2"></i>导出账号列表
                </a></li>
                <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#importModal">
                    <i class="bi bi-file-earmark-arrow-up me-2"></i>批量导入账号
                </a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="#" id="backup-accounts-btn">
                    <i class="bi bi-shield-check me-2"></i>备份配置
                </a></li>
            </ul>
        </div>
    </div>
</div>

<!-- 批量操作工具栏 -->
<div class="row mb-3" id="bulk-actions" style="display: none;">
    <div class="col-12">
        <div class="alert alert-info d-flex justify-content-between align-items-center mb-0">
            <span><i class="bi bi-check-square me-2"></i>已选择 <span id="selected-count">0</span> 个账号</span>
            <div class="btn-group">
                <button class="btn btn-sm btn-success" id="bulk-crawl-btn">
                    <i class="bi bi-play-fill me-1"></i>批量抓取
                </button>
                <button class="btn btn-sm btn-info" id="bulk-refresh-btn">
                    <i class="bi bi-arrow-clockwise me-1"></i>批量刷新
                </button>
                <button class="btn btn-sm btn-warning" id="bulk-tag-btn">
                    <i class="bi bi-tags me-1"></i>批量标签
                </button>
                <button class="btn btn-sm btn-danger" id="bulk-delete-btn">
                    <i class="bi bi-trash me-1"></i>批量删除
                </button>
                <button class="btn btn-sm btn-outline-secondary" id="cancel-selection-btn">
                    <i class="bi bi-x me-1"></i>取消选择
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 账号统计面板 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center border-0 shadow-sm">
            <div class="card-body py-3">
                <h3 class="text-primary mb-1" id="stats-total-accounts">{{ accounts|length }}</h3>
                <p class="text-muted mb-0 small">总账号数</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-0 shadow-sm">
            <div class="card-body py-3">
                <h3 class="text-success mb-1" id="stats-active-accounts">0</h3>
                <p class="text-muted mb-0 small">活跃账号</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-0 shadow-sm">
            <div class="card-body py-3">
                <h3 class="text-info mb-1" id="stats-auto-reply-accounts">0</h3>
                <p class="text-muted mb-0 small">自动回复</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-0 shadow-sm">
            <div class="card-body py-3">
                <h3 class="text-warning mb-1" id="stats-needs-attention">0</h3>
                <p class="text-muted mb-0 small">需要关注</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-list-ul me-2"></i>账号列表
                </h5>
                <div>
                    <button id="filter-accounts-btn" class="btn btn-sm btn-light me-2" data-bs-toggle="collapse" data-bs-target="#filterOptions" aria-expanded="false" aria-controls="filterOptions">
                        <i class="bi bi-funnel me-1"></i> 筛选
                    </button>
                    <div class="btn-group">
                        <button type="button" class="btn btn-sm btn-light dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-sort-down me-1"></i> 排序
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><button class="dropdown-item sort-btn" data-sort="platform">按平台排序</button></li>
                            <li><button class="dropdown-item sort-btn" data-sort="username">按用户名排序</button></li>
                            <li><button class="dropdown-item sort-btn" data-sort="account">按账号ID排序</button></li>
                            <li><button class="dropdown-item sort-btn" data-sort="created">按创建时间排序</button></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 筛选选项 -->
            <div class="collapse" id="filterOptions">
                <div class="card-body bg-light border-bottom">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label for="platform-filter" class="form-label">平台</label>
                            <select class="form-select form-select-sm" id="platform-filter">
                                <option value="">全部平台</option>
                                <option value="twitter">Twitter</option>
                                <option value="weibo">微博</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="tag-filter" class="form-label">标签</label>
                            <select class="form-select form-select-sm" id="tag-filter">
                                <option value="">全部标签</option>
                                {% set tags = [] %}
                                {% for account in accounts %}
                                    {% if account.tag and account.tag not in tags %}
                                        {% set _ = tags.append(account.tag) %}
                                        <option value="{{ account.tag }}">{{ account.tag }}</option>
                                    {% endif %}
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="auto-reply-filter" class="form-label">自动回复</label>
                            <select class="form-select form-select-sm" id="auto-reply-filter">
                                <option value="">全部</option>
                                <option value="enabled">已启用</option>
                                <option value="disabled">未启用</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="ai-filter" class="form-label">AI分析</label>
                            <select class="form-select form-select-sm" id="ai-filter">
                                <option value="">全部</option>
                                <option value="enabled">AI分析</option>
                                <option value="disabled">直接推送</option>
                            </select>
                        </div>
                    </div>
                    <div class="d-flex justify-content-end mt-3">
                        <button id="reset-filter-btn" class="btn btn-sm btn-outline-secondary me-2">
                            <i class="bi bi-arrow-counterclockwise me-1"></i> 重置
                        </button>
                        <button id="apply-filter-btn" class="btn btn-sm btn-primary">
                            <i class="bi bi-check2 me-1"></i> 应用筛选
                        </button>
                    </div>
                </div>
            </div>

            <div class="card-body p-0">
                {% if accounts %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0 accounts-table" id="accounts-table">
                        <thead class="table-light">
                            <tr>
                                <th class="border-0" style="width: 40px;">
                                    <input type="checkbox" class="form-check-input" id="select-all-accounts" title="全选/取消全选">
                                </th>
                                <th class="border-0" style="width: 100px;">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-globe me-1"></i>平台
                                        <button class="btn btn-sm text-muted sort-btn ms-1" data-sort="platform">
                                            <i class="bi bi-arrow-down-up"></i>
                                        </button>
                                    </div>
                                </th>
                                <th class="border-0" style="width: 150px;">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-person me-1"></i>用户名
                                        <button class="btn btn-sm text-muted sort-btn ms-1" data-sort="username">
                                            <i class="bi bi-arrow-down-up"></i>
                                        </button>
                                    </div>
                                </th>
                                <th class="border-0" style="width: 150px;">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-at me-1"></i>账号ID
                                        <button class="btn btn-sm text-muted sort-btn ms-1" data-sort="account">
                                            <i class="bi bi-arrow-down-up"></i>
                                        </button>
                                    </div>
                                </th>
                                <th class="border-0">
                                    <i class="bi bi-tag me-1"></i>标签
                                </th>
                                <th class="border-0">
                                    <i class="bi bi-chat-dots me-1"></i>自动回复
                                </th>
                                <th class="border-0">
                                    <i class="bi bi-robot me-1"></i>AI分析
                                </th>
                                <th class="border-0">
                                    <div class="d-flex align-items-center">
                                        <i class="bi bi-calendar-date me-1"></i>创建时间
                                        <button class="btn btn-sm text-muted sort-btn ms-1" data-sort="created">
                                            <i class="bi bi-arrow-down-up"></i>
                                        </button>
                                    </div>
                                </th>
                                <th class="border-0 text-end">
                                    <i class="bi bi-gear me-1"></i>操作
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for account in accounts %}
                            <tr class="account-row"
                                data-id="{{ account.id }}"
                                data-platform="{{ account.type }}"
                                data-account="{{ account.account_id }}"
                                data-account-id="{{ account.account_id }}"
                                data-account-type="{{ account.type }}"
                                data-account-tag="{{ account.tag }}"
                                data-tag="{{ account.tag }}"
                                data-auto-reply="{{ 'enabled' if account.enable_auto_reply else 'disabled' }}"
                                data-ai="{{ 'disabled' if account.bypass_ai else 'enabled' }}"
                                data-created="{{ account.created_at.timestamp() }}"
                                data-verified="{{ 'true' if account.verified else 'false' }}">
                                <td>
                                    <input type="checkbox" class="form-check-input account-checkbox" value="{{ account.id }}">
                                </td>
                                <!-- 第一列：平台 -->
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if account.type == 'twitter' %}
                                            <span class="badge bg-primary"><i class="bi bi-twitter me-1"></i>Twitter</span>
                                        {% elif account.type == 'weibo' %}
                                            <span class="badge bg-danger"><i class="bi bi-sina-weibo me-1"></i>微博</span>
                                        {% else %}
                                            <span class="badge bg-secondary"><i class="bi bi-globe me-1"></i>{{ account.type }}</span>
                                        {% endif %}
                                        {% if account.verified %}
                                            <i class="bi bi-patch-check-fill text-primary ms-1" title="已验证账号"></i>
                                        {% endif %}
                                    </div>
                                </td>
                                <!-- 第二列：用户名 -->
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if account.avatar_url %}
                                            <img src="{{ account.avatar_url }}" alt="{{ account.display_name or account.account_id }}"
                                                 class="rounded-circle me-2 account-avatar" style="width: 32px; height: 32px; object-fit: cover;">
                                        {% else %}
                                            {% if account.type == 'twitter' %}
                                                <i class="bi bi-twitter text-primary me-2" style="font-size: 1.2rem;"></i>
                                            {% elif account.type == 'weibo' %}
                                                <i class="bi bi-sina-weibo text-danger me-2" style="font-size: 1.2rem;"></i>
                                            {% else %}
                                                <i class="bi bi-person-circle text-secondary me-2" style="font-size: 1.2rem;"></i>
                                            {% endif %}
                                        {% endif %}
                                        <div class="user-name-info">
                                            <div class="fw-medium">{{ account.display_name or account.account_id }}</div>
                                            {% if account.bio %}
                                                <small class="text-muted d-block" title="{{ account.bio }}">{{ account.bio[:30] }}{% if account.bio|length > 30 %}...{% endif %}</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <!-- 第三列：账号ID -->
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="text-muted me-2">@{{ account.account_id }}</span>
                                        {% if account.followers_count %}
                                            <small class="text-muted ms-2">
                                                <i class="bi bi-people me-1"></i>{{ account.followers_count|format_number }}
                                            </small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    {% if account.tag %}
                                        <span class="badge bg-info">{{ account.tag }}</span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if account.enable_auto_reply %}
                                        <span class="badge bg-success"><i class="bi bi-check-circle me-1"></i>已启用</span>
                                    {% else %}
                                        <span class="badge bg-secondary"><i class="bi bi-x-circle me-1"></i>未启用</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if account.bypass_ai %}
                                        <span class="badge bg-warning text-dark" title="所有内容将直接推送，不经过AI分析">
                                            <i class="bi bi-lightning me-1"></i>直接推送
                                        </span>
                                    {% else %}
                                        <span class="badge bg-info">
                                            <i class="bi bi-robot me-1"></i>AI分析
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span title="{{ account.created_at.strftime('%Y-%m-%d %H:%M:%S') }}">
                                        {{ account.created_at.strftime('%Y-%m-%d %H:%M') }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-success run-account-btn"
                                                data-account-id="{{ account.account_id }}" title="抓取此账号内容">
                                            <i class="bi bi-play-fill"></i> <span class="btn-text">抓取</span>
                                            <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                                        </button>
                                        <a href="{{ url_for('edit_account', account_id=account.account_id) }}" class="btn btn-sm btn-outline-primary" title="编辑账号信息">
                                            <i class="bi bi-pencil"></i> 编辑
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger delete-account-btn"
                                                data-account-id="{{ account.account_id }}"
                                                data-account-type="{{ account.type }}"
                                                data-account-tag="{{ account.tag }}"
                                                data-account-avatar="{{ account.avatar_url }}"
                                                title="删除此账号">
                                            <i class="bi bi-trash"></i> 删除
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-people fs-1 text-muted mb-3"></i>
                    <h5>暂无账号</h5>
                    <p class="text-muted">您还没有添加任何社交媒体账号</p>
                    <a href="{{ url_for('add_account') }}" class="btn btn-primary mt-2">
                        <i class="bi bi-plus-lg me-1"></i> 添加第一个账号
                    </a>
                </div>
                {% endif %}
            </div>

            {% if accounts %}
            <div class="card-footer bg-light">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <small class="text-muted">共 <span id="total-accounts">{{ accounts|length }}</span> 个账号</small>
                    </div>
                    <a href="{{ url_for('add_account') }}" class="btn btn-primary btn-sm">
                        <i class="bi bi-plus-lg me-1"></i> 添加账号
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 账号详情侧边栏 -->
<div class="offcanvas offcanvas-end" tabindex="-1" id="accountDetailsOffcanvas" aria-labelledby="accountDetailsOffcanvasLabel">
    <div class="offcanvas-header">
        <h5 class="offcanvas-title" id="accountDetailsOffcanvasLabel">账号详情</h5>
        <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
    </div>
    <div class="offcanvas-body" id="account-details-content">
        <!-- 账号详情内容将通过JavaScript动态加载 -->
        <div class="d-flex justify-content-center align-items-center h-100">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
        </div>
    </div>
</div>
<!-- 删除确认模态框 -->
<div class="modal" id="delete-confirm-modal" tabindex="-1" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>确认删除
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3" id="delete-account-avatar">
                    <!-- 头像将通过JavaScript动态添加 -->
                </div>
                <p>您确定要删除账号 <strong id="delete-account-name">@username</strong> 吗？</p>
                <p class="text-danger">此操作不可撤销，账号相关的所有数据将被永久删除。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="delete-account-form" action="" method="post" class="delete-account-form">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash me-1"></i>确认删除
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 任务状态模态框 -->
<div class="modal fade" id="taskStatusModal" tabindex="-1" aria-labelledby="taskStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="taskStatusModalLabel">
                    <i class="bi bi-activity me-2"></i>监控任务状态
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="关闭"></button>
            </div>
            <div class="modal-body">
                <div id="task-status-content">
                    <!-- 任务状态头部 -->
                    <div class="alert alert-primary d-flex align-items-center mb-3" id="task-status-alert">
                        <div class="spinner-border spinner-border-sm text-primary me-3" role="status" id="task-spinner">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <div>
                            <h6 class="alert-heading mb-1">任务进行中</h6>
                            <p class="mb-0" id="task-message">正在准备任务...</p>
                        </div>
                    </div>

                    <!-- 任务进度条 -->
                    <div class="progress mb-3" id="task-progress-container" style="height: 10px; display: none;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" id="task-progress" role="progressbar" style="width: 0%"></div>
                    </div>

                    <!-- 任务详情 -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-0 shadow-sm mb-3">
                                <div class="card-header bg-light">
                                    <h6 class="card-title mb-0">
                                        <i class="bi bi-info-circle me-2 text-primary"></i>任务信息
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-group list-group-flush">
                                        <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                            <span>
                                                <i class="bi bi-check2-circle me-2 text-primary"></i>状态
                                            </span>
                                            <span class="badge bg-info" id="task-status-text">准备中</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                            <span>
                                                <i class="bi bi-clock me-2 text-primary"></i>开始时间
                                            </span>
                                            <span id="task-start-time">-</span>
                                        </li>
                                        <li class="list-group-item d-flex justify-content-between align-items-center px-0">
                                            <span>
                                                <i class="bi bi-hourglass-split me-2 text-primary"></i>运行时间
                                            </span>
                                            <span id="task-duration">0秒</span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-0 shadow-sm mb-3">
                                <div class="card-header bg-light">
                                    <h6 class="card-title mb-0">
                                        <i class="bi bi-bar-chart me-2 text-primary"></i>处理统计
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <div class="p-3">
                                                <h3 class="text-primary mb-0" id="task-posts-count">0</h3>
                                                <p class="text-muted small mb-0">处理的帖子</p>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="p-3">
                                                <h3 class="text-success mb-0" id="task-relevant-count">0</h3>
                                                <p class="text-muted small mb-0">相关内容</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 任务日志 -->
                    <div class="card border-0 shadow-sm" id="task-log-container">
                        <div class="card-header bg-light">
                            <h6 class="card-title mb-0">
                                <i class="bi bi-journal-text me-2 text-primary"></i>任务日志
                            </h6>
                        </div>
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush" id="task-log">
                                <div class="list-group-item">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">任务初始化</h6>
                                        <small class="text-muted" id="task-init-time">刚刚</small>
                                    </div>
                                    <p class="mb-1">系统正在准备执行监控任务...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-1"></i>关闭
                </button>
                <a href="{{ url_for('results') }}" class="btn btn-primary" id="view-results-btn" style="display: none;">
                    <i class="bi bi-search me-1"></i>查看结果
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 批量导入模态框 -->
<div class="modal fade" id="importModal" tabindex="-1" aria-labelledby="importModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="importModalLabel">
                    <i class="bi bi-file-earmark-arrow-up me-2"></i>批量导入账号
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="关闭"></button>
            </div>
            <div class="modal-body">
                <!-- 导入步骤指示器 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-between">
                            <div class="step-item active" id="step-1">
                                <div class="step-circle">1</div>
                                <div class="step-label">选择文件</div>
                            </div>
                            <div class="step-item" id="step-2">
                                <div class="step-circle">2</div>
                                <div class="step-label">预览数据</div>
                            </div>
                            <div class="step-item" id="step-3">
                                <div class="step-circle">3</div>
                                <div class="step-label">导入完成</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 步骤1：文件选择 -->
                <div id="import-step-1">
                    <div class="row mb-3">
                        <div class="col-12">
                            <h6>支持的文件格式</h6>
                            <p class="text-muted">支持 CSV、Excel (.xlsx) 文件格式</p>
                        </div>
                    </div>

                    <!-- 文件拖放区域 -->
                    <div class="file-drop-zone" id="file-drop-zone">
                        <i class="bi bi-cloud-upload fs-1 text-muted mb-3"></i>
                        <h5>拖放文件到此处或点击选择</h5>
                        <p class="text-muted">支持 CSV、Excel 文件，最大 10MB</p>
                        <input type="file" id="file-input" accept=".csv,.xlsx,.xls" style="display: none;">
                        <button type="button" class="btn btn-primary" onclick="document.getElementById('file-input').click()">
                            <i class="bi bi-folder2-open me-1"></i>选择文件
                        </button>
                    </div>

                    <!-- 文件信息 -->
                    <div id="file-info" style="display: none;" class="mt-3">
                        <div class="alert alert-info">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-file-earmark-text fs-4 me-3"></i>
                                <div>
                                    <h6 class="mb-1" id="file-name">文件名</h6>
                                    <small class="text-muted" id="file-details">文件详情</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 模板下载 -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0"><i class="bi bi-download me-2"></i>下载模板</h6>
                                </div>
                                <div class="card-body">
                                    <p class="mb-3">如果您是第一次使用批量导入功能，建议先下载模板文件：</p>
                                    <div class="d-grid gap-2 d-md-flex">
                                        <a href="/api/accounts/template/csv" class="btn btn-outline-primary">
                                            <i class="bi bi-file-earmark-text me-1"></i>CSV 模板
                                        </a>
                                        <a href="/api/accounts/template/excel" class="btn btn-outline-primary">
                                            <i class="bi bi-file-earmark-excel me-1"></i>Excel 模板
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 步骤2：数据预览 -->
                <div id="import-step-2" style="display: none;">
                    <div class="row mb-3">
                        <div class="col-12">
                            <h6>数据预览</h6>
                            <p class="text-muted">请确认以下数据是否正确，然后点击导入按钮</p>
                        </div>
                    </div>

                    <!-- 导入统计 -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body py-2">
                                    <h4 class="text-primary mb-1" id="preview-total">0</h4>
                                    <small class="text-muted">总记录数</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body py-2">
                                    <h4 class="text-success mb-1" id="preview-valid">0</h4>
                                    <small class="text-muted">有效记录</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card text-center">
                                <div class="card-body py-2">
                                    <h4 class="text-warning mb-1" id="preview-invalid">0</h4>
                                    <small class="text-muted">无效记录</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 数据表格 -->
                    <div class="preview-table">
                        <table class="table table-sm table-bordered" id="preview-data-table">
                            <thead class="table-light">
                                <tr>
                                    <th>状态</th>
                                    <th>平台</th>
                                    <th>账号ID</th>
                                    <th>显示名称</th>
                                    <th>标签</th>
                                    <th>自动回复</th>
                                    <th>AI分析</th>
                                </tr>
                            </thead>
                            <tbody id="preview-data-body">
                                <!-- 数据将通过JavaScript填充 -->
                            </tbody>
                        </table>
                    </div>

                    <!-- 错误信息 -->
                    <div id="import-errors" style="display: none;">
                        <div class="alert alert-warning">
                            <h6><i class="bi bi-exclamation-triangle me-2"></i>发现以下问题：</h6>
                            <ul id="error-list" class="mb-0">
                                <!-- 错误列表将通过JavaScript填充 -->
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 步骤3：导入进度 -->
                <div id="import-step-3" style="display: none;">
                    <div class="text-center">
                        <div class="import-progress" id="import-progress">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">导入中...</span>
                            </div>
                            <h5>正在导入账号...</h5>
                            <p class="text-muted">请稍候，正在处理您的数据</p>

                            <!-- 进度条 -->
                            <div class="progress mb-3">
                                <div class="progress-bar progress-bar-striped progress-bar-animated"
                                     id="import-progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>

                            <div id="import-status">
                                <small class="text-muted">已处理 <span id="processed-count">0</span> / <span id="total-count">0</span> 条记录</small>
                            </div>
                        </div>

                        <!-- 导入结果 -->
                        <div class="import-result" id="import-result" style="display: none;">
                            <div class="alert alert-success">
                                <i class="bi bi-check-circle-fill fs-1 text-success mb-3"></i>
                                <h4>导入完成！</h4>
                                <p class="mb-3">成功导入 <span id="success-count">0</span> 个账号</p>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-body text-center">
                                                <h5 class="text-success" id="final-success-count">0</h5>
                                                <small class="text-muted">成功导入</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card">
                                            <div class="card-body text-center">
                                                <h5 class="text-danger" id="final-error-count">0</h5>
                                                <small class="text-muted">导入失败</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="import-cancel-btn">取消</button>
                <button type="button" class="btn btn-primary" id="import-next-btn" disabled>下一步</button>
                <button type="button" class="btn btn-success" id="import-start-btn" style="display: none;">开始导入</button>
                <button type="button" class="btn btn-primary" id="import-finish-btn" style="display: none;" data-bs-dismiss="modal">完成</button>
            </div>
        </div>
    </div>
</div>

<!-- 批量标签模态框 -->
<div class="modal fade" id="bulkTagModal" tabindex="-1" aria-labelledby="bulkTagModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="bulkTagModalLabel">
                    <i class="bi bi-tags me-2"></i>批量设置标签
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
            </div>
            <div class="modal-body">
                <form id="bulk-tag-form">
                    <div class="mb-3">
                        <label for="bulk-tag-input" class="form-label">标签名称</label>
                        <input type="text" class="form-control" id="bulk-tag-input" placeholder="输入标签名称">
                        <div class="form-text">将为选中的账号设置此标签</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">已选择的账号</label>
                        <div id="selected-accounts-list" class="border rounded p-2 bg-light" style="max-height: 200px; overflow-y: auto;">
                            <!-- 选中的账号列表将通过JavaScript填充 -->
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-warning" id="bulk-tag-confirm-btn">
                    <i class="bi bi-tags me-1"></i>设置标签
                </button>
            </div>
        </div>
    </div>
</div>


{% endblock %}

{% block extra_js %}
<!-- Sortable.js库 - 用于拖放排序 -->
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
    // 全局变量
    let taskStatusModal;
    let taskStatusInterval;
    let taskStartTime;
    let lastUpdateTime;
    let currentSortField = 'platform';
    let currentSortDirection = 'asc';
    let activeFilters = {};




    // 全局变量
    let deleteModal = null;

    // 检查Flash消息
    function checkFlashMessages() {
        // 查找包含"账号已成功删除"的消息
        const successMessages = document.querySelectorAll('.alert-success');
        successMessages.forEach(message => {
            if (message.textContent.includes('账号已成功删除')) {
                // 如果找到了，隐藏它
                message.style.display = 'none';

                // 关闭任何可能打开的模态框
                const modals = document.querySelectorAll('.modal.show');
                modals.forEach(modal => {
                    const bsModal = bootstrap.Modal.getInstance(modal);
                    if (bsModal) {
                        bsModal.hide();
                    }
                });
            }
        });
    }

    // 初始化页面
    document.addEventListener('DOMContentLoaded', function() {
        // 检查Flash消息
        checkFlashMessages();

        // 初始化删除确认模态框
        deleteModal = new bootstrap.Modal(document.getElementById('delete-confirm-modal'), {
            backdrop: 'static',
            keyboard: false
        });

        // 为所有删除按钮添加点击事件
        document.querySelectorAll('.delete-account-btn').forEach(button => {
            button.addEventListener('click', function() {
                const accountId = this.getAttribute('data-account-id');
                const accountType = this.getAttribute('data-account-type');
                const accountTag = this.getAttribute('data-account-tag') || '无';
                const avatarUrl = this.getAttribute('data-account-avatar');

                // 设置删除表单的action
                document.getElementById('delete-account-form').action = `/accounts/delete/${accountId}`;

                // 设置账号名称
                document.getElementById('delete-account-name').textContent = accountId;

                // 设置头像
                const avatarContainer = document.getElementById('delete-account-avatar');
                if (avatarUrl) {
                    avatarContainer.innerHTML = `<img src="${avatarUrl}" alt="${accountId}" class="rounded-circle" style="width: 64px; height: 64px; object-fit: cover;">`;
                } else {
                    if (accountType === 'twitter') {
                        avatarContainer.innerHTML = `<i class="bi bi-twitter fs-1 text-primary"></i>`;
                    } else if (accountType === 'weibo') {
                        avatarContainer.innerHTML = `<i class="bi bi-sina-weibo fs-1 text-danger"></i>`;
                    } else {
                        avatarContainer.innerHTML = `<i class="bi bi-globe fs-1 text-secondary"></i>`;
                    }
                }

                // 显示模态框
                deleteModal.show();
            });
        });

        // 为删除表单添加提交事件
        document.getElementById('delete-account-form').addEventListener('submit', function(event) {
            // 获取提交按钮
            const submitButton = this.querySelector('button[type="submit"]');

            // 如果按钮已经被禁用，说明表单正在提交中，阻止重复提交
            if (submitButton.disabled) {
                event.preventDefault();
                return false;
            }

            // 禁用按钮，防止重复点击
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>删除中...';
        });

        // 初始化模态框
        taskStatusModal = new bootstrap.Modal(document.getElementById('taskStatusModal'));

        // 初始化当前时间
        lastUpdateTime = new Date();
        const taskInitTime = document.getElementById('task-init-time');
        if (taskInitTime) {
            taskInitTime.textContent = formatTime(lastUpdateTime);
        }

        // 初始化事件监听器
        initEventListeners();

        // 初始化排序和筛选
        initSortAndFilter();

        // 拖放排序功能已移除

        // 检查并处理Flash消息
        checkFlashMessages();
    });

    // 初始化事件监听器
    function initEventListeners() {
        // 绑定全局抓取按钮事件
        const runAllBtn = document.getElementById('run-all-accounts-btn');
        if (runAllBtn) {
            runAllBtn.addEventListener('click', function() {
                handleRunButtonClick(this);
            });
        }

        // 绑定单个账号抓取按钮事件
        document.querySelectorAll('.run-account-btn').forEach(button => {
            button.addEventListener('click', function() {
                const accountId = this.getAttribute('data-account-id');
                handleRunButtonClick(this, accountId);
            });
        });

        // 绑定账号行点击事件 - 显示详情
        document.querySelectorAll('.account-row').forEach(row => {
            row.addEventListener('dblclick', function() {
                const accountId = this.getAttribute('data-id');
                showAccountDetails(accountId);
            });
        });
    }

    // 处理运行按钮点击
    function handleRunButtonClick(button, accountId = null) {
        // 显示加载状态
        const btnText = button.querySelector('.btn-text');
        const spinner = button.querySelector('.spinner-border');

        button.disabled = true;
        btnText.textContent = accountId ? '启动中...' : '正在启动...';
        spinner.classList.remove('d-none');

        // 运行任务
        runTask(accountId)
            .catch(error => {
                // 显示错误通知
                showToast('错误', `启动任务失败: ${error.message}`, 'danger');
            })
            .finally(() => {
                // 恢复按钮状态
                button.disabled = false;
                btnText.textContent = accountId ? '抓取' : '立即抓取所有账号';
                spinner.classList.add('d-none');
            });
    }



    // 初始化排序和筛选功能
    function initSortAndFilter() {
        // 排序按钮点击事件
        document.querySelectorAll('.sort-btn').forEach(button => {
            button.addEventListener('click', function() {
                const sortField = this.getAttribute('data-sort');

                // 如果点击的是当前排序字段，则切换排序方向
                if (sortField === currentSortField) {
                    currentSortDirection = currentSortDirection === 'asc' ? 'desc' : 'asc';
                } else {
                    currentSortField = sortField;
                    currentSortDirection = 'asc';
                }

                // 执行排序
                sortAccounts(currentSortField, currentSortDirection);

                // 更新排序按钮图标
                updateSortButtonIcons();
            });
        });

        // 筛选按钮点击事件
        const applyFilterBtn = document.getElementById('apply-filter-btn');
        if (applyFilterBtn) {
            applyFilterBtn.addEventListener('click', function() {
                // 收集筛选条件
                activeFilters = {
                    platform: document.getElementById('platform-filter').value,
                    tag: document.getElementById('tag-filter').value,
                    autoReply: document.getElementById('auto-reply-filter').value,
                    ai: document.getElementById('ai-filter').value
                };

                // 应用筛选
                filterAccounts();

                // 关闭筛选面板
                const filterCollapse = bootstrap.Collapse.getInstance(document.getElementById('filterOptions'));
                if (filterCollapse) {
                    filterCollapse.hide();
                }

                // 更新账号计数
                updateAccountCount();
            });
        }

        // 重置筛选按钮点击事件
        const resetFilterBtn = document.getElementById('reset-filter-btn');
        if (resetFilterBtn) {
            resetFilterBtn.addEventListener('click', function() {
                // 重置筛选表单
                document.getElementById('platform-filter').value = '';
                document.getElementById('tag-filter').value = '';
                document.getElementById('auto-reply-filter').value = '';
                document.getElementById('ai-filter').value = '';

                // 清空筛选条件
                activeFilters = {};

                // 显示所有账号
                document.querySelectorAll('.account-row').forEach(row => {
                    row.style.display = '';
                });

                // 更新账号计数
                updateAccountCount();
            });
        }
    }

    // 显示账号详情
    function showAccountDetails(accountId) {
        // 获取账号详情侧边栏
        const offcanvas = document.getElementById('accountDetailsOffcanvas');
        const contentArea = document.getElementById('account-details-content');

        if (offcanvas && contentArea) {
            // 显示加载中
            contentArea.innerHTML = `
                <div class="d-flex justify-content-center align-items-center h-100">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            `;

            // 显示侧边栏
            const bsOffcanvas = new bootstrap.Offcanvas(offcanvas);
            bsOffcanvas.show();

            // 加载账号详情
            fetch(`/api/accounts/${accountId}`)
                .then(response => {
                    // 检查响应是否成功
                    if (!response.ok) {
                        throw new Error(`服务器返回错误: ${response.status} ${response.statusText}`);
                    }
                    // 检查Content-Type是否为application/json
                    const contentType = response.headers.get('content-type');
                    if (!contentType || !contentType.includes('application/json')) {
                        throw new Error(`预期JSON响应，但收到: ${contentType}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        // API返回的数据在data.data字段中
                        const accountData = data.data;
                        if (!accountData) {
                            throw new Error('API返回的数据格式不正确，缺少account数据');
                        }
                        console.log('账号数据:', accountData); // 调试日志
                        renderAccountDetails(accountData, contentArea);
                    } else {
                        contentArea.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                加载账号详情失败: ${data.message || '未知错误'}
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    contentArea.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            加载账号详情失败: ${error.message}
                        </div>
                        <button class="btn btn-primary mt-3" onclick="showAccountDetails('${accountId}')">
                            <i class="bi bi-arrow-clockwise me-1"></i>重试
                        </button>
                    `;
                });
        }
    }

    // 渲染账号详情
    function renderAccountDetails(account, container) {
        if (!account || !container) return;

        // 格式化创建时间
        const createdAt = new Date(account.created_at);
        const formattedDate = createdAt.toLocaleString();

        // 格式化加入日期
        let joinDateFormatted = '未知';
        if (account.join_date) {
            const joinDate = new Date(account.join_date);
            joinDateFormatted = joinDate.toLocaleDateString('zh-CN', { year: 'numeric', month: 'long', day: 'numeric' });
        }

        // 设置平台图标
        let platformIcon = 'globe';
        if (account.type === 'twitter') platformIcon = 'twitter';
        else if (account.type === 'weibo') platformIcon = 'sina-weibo';

        // 格式化粉丝数和关注数
        const formatNumber = (num) => {
            if (num === undefined || num === null) return '0';
            if (num < 1000) return num.toString();
            if (num < 10000) return (num / 1000).toFixed(1) + 'K';
            if (num < 1000000) return (num / 10000).toFixed(1) + '万';
            return (num / 1000000).toFixed(1) + 'M';
        };

        // 渲染详情
        container.innerHTML = `
            <div class="account-details">
                <div class="account-profile-card mb-4">
                    <div class="account-header">
                        <div class="account-banner" style="background-color: ${account.type === 'twitter' ? '#1DA1F2' : '#E6162D'}"></div>
                        <div class="account-avatar-container">
                            ${account.avatar_url
                                ? `<img src="${account.avatar_url}" alt="${account.account_id}" class="account-avatar">`
                                : `<i class="bi bi-${platformIcon} fs-1 text-white account-avatar-icon"></i>`
                            }
                        </div>
                    </div>

                    <div class="account-info">
                        <div class="account-name-container">
                            <h3 class="account-display-name">
                                ${account.display_name || account.account_id}
                                ${account.verified ? '<i class="bi bi-patch-check-fill text-primary ms-1"></i>' : ''}
                            </h3>
                            <p class="account-username">@${account.account_id}</p>
                        </div>

                        ${account.bio ? `<p class="account-bio">${account.bio}</p>` : ''}

                        <div class="account-meta">
                            ${account.profession ? `
                                <div class="meta-item">
                                    <i class="bi bi-briefcase"></i> ${account.profession}
                                </div>
                            ` : ''}

                            ${account.location ? `
                                <div class="meta-item">
                                    <i class="bi bi-geo-alt"></i> ${account.location}
                                </div>
                            ` : ''}

                            ${account.website ? `
                                <div class="meta-item">
                                    <i class="bi bi-link-45deg"></i>
                                    <a href="${account.website}" target="_blank">${account.website}</a>
                                </div>
                            ` : ''}

                            ${account.join_date ? `
                                <div class="meta-item">
                                    <i class="bi bi-calendar3"></i> ${joinDateFormatted} 加入
                                </div>
                            ` : ''}

                            <div class="meta-item">
                                <i class="bi bi-tag"></i> ${account.tag || '无标签'}
                            </div>
                        </div>

                        <div class="account-stats">
                            <div class="stat-item">
                                <span class="stat-value">${formatNumber(account.following_count)}</span>
                                <span class="stat-label">正在关注</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-value">${formatNumber(account.followers_count)}</span>
                                <span class="stat-label">关注者</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mb-3 shadow-sm">
                    <div class="card-header bg-light">
                        <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>基本信息</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span>ID</span>
                                <span class="badge bg-secondary">${account.id}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span>平台</span>
                                <span>${account.type}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span>账号</span>
                                <span>${account.account_id}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span>创建时间</span>
                                <span>${formattedDate}</span>
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="card mb-3 shadow-sm">
                    <div class="card-header bg-light">
                        <h6 class="mb-0"><i class="bi bi-gear me-2"></i>功能设置</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span>自动回复</span>
                                ${account.enable_auto_reply
                                    ? '<span class="badge bg-success"><i class="bi bi-check-circle me-1"></i>已启用</span>'
                                    : '<span class="badge bg-secondary"><i class="bi bi-x-circle me-1"></i>未启用</span>'}
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <span>AI分析</span>
                                ${account.bypass_ai
                                    ? '<span class="badge bg-warning text-dark"><i class="bi bi-lightning me-1"></i>直接推送</span>'
                                    : '<span class="badge bg-info"><i class="bi bi-robot me-1"></i>AI分析</span>'}
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="d-grid gap-2">
                    ${account.account_id ?
                      `<a href="/accounts/edit/${account.account_id}" class="btn btn-primary">
                        <i class="bi bi-pencil me-1"></i>编辑账号
                       </a>` :
                      `<button class="btn btn-secondary" disabled>
                        <i class="bi bi-pencil me-1"></i>无法编辑 (账号ID不存在)
                       </button>`
                    }
                    <button class="btn btn-success run-account-detail-btn" data-account-id="${account.account_id}">
                        <i class="bi bi-play-fill me-1"></i>抓取此账号
                    </button>
                    <button class="btn btn-info refresh-account-info-btn mt-2" data-account-id="${account.account_id}">
                        <i class="bi bi-arrow-clockwise me-1"></i>刷新账号信息
                    </button>
                </div>
            </div>
        `;



        // 绑定抓取按钮事件
        container.querySelectorAll('.run-account-detail-btn').forEach(button => {
            button.addEventListener('click', function() {
                const accountId = this.getAttribute('data-account-id');
                handleRunButtonClick(this, accountId);
            });
        });

        // 绑定刷新账号信息按钮事件
        container.querySelectorAll('.refresh-account-info-btn').forEach(button => {
            button.addEventListener('click', function() {
                const accountId = this.getAttribute('data-account-id');
                refreshAccountInfo(this, accountId);
            });
        });
    }

    // 排序账号
    function sortAccounts(field, direction) {
        const table = document.getElementById('accounts-table');
        if (!table) return;

        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr.account-row'));

        // 根据字段和方向排序
        rows.sort((a, b) => {
            let valueA, valueB;

            // 获取排序值
            if (field === 'platform') {
                valueA = a.getAttribute('data-platform');
                valueB = b.getAttribute('data-platform');
            } else if (field === 'username') {
                // 获取用户名，优先使用display_name，否则使用account_id
                const userNameA = a.querySelector('.fw-medium')?.textContent?.trim() || a.getAttribute('data-account');
                const userNameB = b.querySelector('.fw-medium')?.textContent?.trim() || b.getAttribute('data-account');
                valueA = userNameA.toLowerCase();
                valueB = userNameB.toLowerCase();
            } else if (field === 'account') {
                valueA = a.getAttribute('data-account');
                valueB = b.getAttribute('data-account');
            } else if (field === 'created') {
                valueA = parseFloat(a.getAttribute('data-created'));
                valueB = parseFloat(b.getAttribute('data-created'));
            }

            // 比较
            if (valueA === valueB) return 0;

            // 根据排序方向返回结果
            const result = valueA < valueB ? -1 : 1;
            return direction === 'asc' ? result : -result;
        });

        // 重新排列行
        rows.forEach(row => tbody.appendChild(row));
    }

    // 更新排序按钮图标
    function updateSortButtonIcons() {
        document.querySelectorAll('.sort-btn').forEach(button => {
            const field = button.getAttribute('data-sort');
            const icon = button.querySelector('i');

            if (field === currentSortField) {
                icon.className = currentSortDirection === 'asc'
                    ? 'bi bi-arrow-down'
                    : 'bi bi-arrow-up';
            } else {
                icon.className = 'bi bi-arrow-down-up';
            }
        });
    }

    // 筛选账号
    function filterAccounts() {
        const rows = document.querySelectorAll('.account-row');

        rows.forEach(row => {
            let visible = true;

            // 检查每个筛选条件
            if (activeFilters.platform && row.getAttribute('data-platform') !== activeFilters.platform) {
                visible = false;
            }

            if (activeFilters.tag && row.getAttribute('data-tag') !== activeFilters.tag) {
                visible = false;
            }

            if (activeFilters.autoReply && row.getAttribute('data-auto-reply') !== activeFilters.autoReply) {
                visible = false;
            }

            if (activeFilters.ai && row.getAttribute('data-ai') !== activeFilters.ai) {
                visible = false;
            }

            // 设置行的可见性
            row.style.display = visible ? '' : 'none';
        });
    }

    // 更新账号计数
    function updateAccountCount() {
        const totalElement = document.getElementById('total-accounts');
        if (!totalElement) return;

        // 计算可见行数
        const visibleRows = document.querySelectorAll('.account-row[style=""],.account-row:not([style])').length;
        totalElement.textContent = visibleRows;
    }

    // 显示提示消息
    function showToast(title, message, type = 'info') {
        // 检查是否已存在toast容器
        let toastContainer = document.querySelector('.toast-container');

        // 如果不存在，创建一个
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            document.body.appendChild(toastContainer);
        }

        // 创建toast元素
        const toastId = 'toast-' + Date.now();
        const toastHTML = `
            <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header bg-${type} text-white">
                    <strong class="me-auto">${title}</strong>
                    <small>刚刚</small>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;

        // 添加到容器
        toastContainer.insertAdjacentHTML('beforeend', toastHTML);

        // 初始化并显示toast
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
        toast.show();

        // 自动移除
        toastElement.addEventListener('hidden.bs.toast', function() {
            toastElement.remove();
        });

        // 确保关闭按钮正常工作
        const closeButton = toastElement.querySelector('.btn-close');
        if (closeButton) {
            closeButton.addEventListener('click', function() {
                toast.hide();
            });
        }

        return toast;
    }

    // 格式化时间
    function formatTime(date) {
        if (!date) return '未知';

        const now = new Date();
        const diff = Math.floor((now - date) / 1000);

        if (diff < 60) return '刚刚';
        if (diff < 3600) return `${Math.floor(diff / 60)}分钟前`;
        if (diff < 86400) return `${Math.floor(diff / 3600)}小时前`;

        return date.toLocaleString();
    }

    // 初始化拖放排序
    function initDragAndDrop() {
        const tbody = document.querySelector('#accounts-table tbody');
        if (!tbody) return;

        // 加载保存的排序顺序
        loadSavedOrder();

        // 初始化Sortable.js
        accountSortable = new Sortable(tbody, {
            handle: '.drag-handle', // 只能通过拖动手柄拖动
            animation: 150, // 动画时间
            ghostClass: 'sortable-ghost', // 拖动时的样式
            chosenClass: 'sortable-chosen', // 选中时的样式
            dragClass: 'sortable-drag', // 拖动元素的样式
            onEnd: function(evt) {
                // 保存新的排序顺序
                saveOrder();

                // 显示提示
                showToast('成功', '账号排序已保存', 'success');
            }
        });

        // 添加自定义样式
        const style = document.createElement('style');
        style.textContent = `
            .sortable-ghost {
                opacity: 0.5;
                background-color: #f8f9fa;
            }
            .sortable-chosen {
                background-color: #e9ecef;
            }
            .sortable-drag {
                background-color: #ffffff;
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            }
            .account-row.pinned {
                background-color: rgba(13, 110, 253, 0.05);
            }
            .account-row.pinned .drag-handle i {
                color: #0d6efd !important;
            }
        `;
        document.head.appendChild(style);

        // 添加固定/取消固定按钮
        document.querySelectorAll('.account-row').forEach(row => {
            const dragHandle = row.querySelector('.drag-handle');
            if (dragHandle) {
                const pinButton = document.createElement('button');
                pinButton.className = 'btn btn-sm btn-link p-0 ms-2 pin-btn';
                pinButton.innerHTML = '<i class="bi bi-pin text-muted"></i>';
                pinButton.title = '固定到顶部';
                pinButton.style.display = 'none';

                // 鼠标悬停时显示固定按钮
                row.addEventListener('mouseenter', () => {
                    pinButton.style.display = 'inline-block';
                });
                row.addEventListener('mouseleave', () => {
                    pinButton.style.display = 'none';
                });

                // 点击固定按钮
                pinButton.addEventListener('click', (e) => {
                    e.stopPropagation(); // 阻止事件冒泡

                    const isPinned = row.classList.contains('pinned');
                    if (isPinned) {
                        // 取消固定
                        row.classList.remove('pinned');
                        pinButton.innerHTML = '<i class="bi bi-pin text-muted"></i>';
                        pinButton.title = '固定到顶部';
                    } else {
                        // 固定
                        row.classList.add('pinned');
                        pinButton.innerHTML = '<i class="bi bi-pin-fill text-primary"></i>';
                        pinButton.title = '取消固定';

                        // 移动到顶部
                        tbody.insertBefore(row, tbody.firstChild);
                    }

                    // 保存排序
                    saveOrder();
                });

                dragHandle.appendChild(pinButton);
            }
        });
    }

    // 保存排序顺序
    function saveOrder() {
        const rows = document.querySelectorAll('.account-row');
        const order = Array.from(rows).map(row => {
            return {
                id: row.getAttribute('data-id'),
                accountId: row.getAttribute('data-account-id'),
                pinned: row.classList.contains('pinned')
            };
        });

        // 保存到localStorage
        localStorage.setItem('accountOrder', JSON.stringify(order));
    }

    // 加载保存的排序顺序
    function loadSavedOrder() {
        const savedOrder = localStorage.getItem('accountOrder');
        if (!savedOrder) return;

        try {
            const order = JSON.parse(savedOrder);
            const tbody = document.querySelector('#accounts-table tbody');
            if (!tbody) return;

            // 按保存的顺序重新排列行
            order.forEach(item => {
                const row = document.querySelector(`.account-row[data-id="${item.id}"]`);
                if (row) {
                    // 如果行被固定，添加固定样式
                    if (item.pinned) {
                        row.classList.add('pinned');
                        const pinBtn = row.querySelector('.pin-btn');
                        if (pinBtn) {
                            pinBtn.innerHTML = '<i class="bi bi-pin-fill text-primary"></i>';
                            pinBtn.title = '取消固定';
                        }
                    }

                    // 移动行到正确位置
                    tbody.appendChild(row);
                }
            });
        } catch (error) {
            console.error('加载账号排序时出错:', error);
        }
    }

    // 显示Twitter配置模态框
    function showTwitterConfigModal(message, configUrl) {
        // 创建模态框HTML
        const modalHtml = `
            <div class="modal fade" id="twitterConfigModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header bg-warning text-dark">
                            <h5 class="modal-title">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>Twitter配置缺失
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="text-center mb-3">
                                <i class="bi bi-twitter text-primary" style="font-size: 3rem;"></i>
                            </div>
                            <p class="text-center">${message}</p>
                            <div class="alert alert-info">
                                <h6><i class="bi bi-info-circle me-2"></i>解决方法：</h6>
                                <ol class="mb-0">
                                    <li>点击下方"前往设置"按钮</li>
                                    <li>在Twitter设置页面配置登录凭据</li>
                                    <li>保存配置后重新尝试刷新</li>
                                </ol>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <a href="${configUrl}" class="btn btn-primary">
                                <i class="bi bi-gear me-1"></i>前往设置
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 移除已存在的模态框
        const existingModal = document.getElementById('twitterConfigModal');
        if (existingModal) {
            existingModal.remove();
        }

        // 添加新模态框到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('twitterConfigModal'));
        modal.show();

        // 模态框关闭后清理
        document.getElementById('twitterConfigModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    }

    // 刷新账号信息函数
    function refreshAccountInfo(button, accountId) {
        // 显示加载状态
        const originalText = button.innerHTML;
        button.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 刷新中...';
        button.disabled = true;

        // 调用API刷新账号信息
        fetch(`/api/accounts/${accountId}/refresh`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.message || `HTTP错误: ${response.status}`);
                }).catch(() => {
                    throw new Error(`HTTP错误: ${response.status}`);
                });
            }
            return response.json();
        })
        .then(data => {
            // 恢复按钮状态
            button.innerHTML = originalText;
            button.disabled = false;

            if (data.success) {
                // 显示成功消息
                showToast('刷新成功', '账号信息已成功更新', 'success');

                // 重新加载账号详情
                showAccountDetails(accountId);
            } else {
                // 处理特殊错误类型
                if (data.action === 'configure_twitter') {
                    // Twitter配置缺失的特殊处理
                    showTwitterConfigModal(data.message, data.config_url);
                } else {
                    // 显示普通错误消息
                    showToast('刷新失败', data.message || '未知错误', 'danger');
                }
            }
        })
        .catch(error => {
            // 恢复按钮状态
            button.innerHTML = originalText;
            button.disabled = false;

            // 显示错误消息
            console.error('Error:', error);
            showToast('刷新失败', error.message, 'danger');
        });
    }

    // 运行任务函数
    function runTask(accountId = null) {
        return new Promise((resolve, reject) => {
            // 重置模态框状态
            resetTaskModal();

            // 显示模态框
            taskStatusModal.show();

            // 准备请求数据
            const requestData = {};
            if (accountId) {
                requestData.account_id = accountId;
                updateTaskMessage(`正在抓取账号: ${accountId}`);
                addTaskLogEntry('开始任务', `开始抓取账号: ${accountId}`);
            } else {
                updateTaskMessage('正在抓取所有账号');
                addTaskLogEntry('开始任务', '开始抓取所有监控账号');
            }

            // 发送请求启动任务
            fetch('/api/tasks/run', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData),
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP错误: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // 记录开始时间
                    taskStartTime = new Date();

                    // 更新状态警报
                    updateTaskAlert('进行中', 'primary');

                    // 更新状态
                    updateTaskStatus(data.status);

                    // 添加日志条目
                    addTaskLogEntry('任务启动', '监控任务已成功启动');

                    // 启动定时查询状态
                    taskStatusInterval = setInterval(checkTaskStatus, 1000);

                    // 显示进度条
                    const progressContainer = document.getElementById('task-progress-container');
                    if (progressContainer) {
                        progressContainer.style.display = 'block';
                    }

                    // 解析Promise
                    resolve(data);
                } else {
                    // 显示错误
                    updateTaskAlert('失败', 'danger');
                    updateTaskMessage(`启动任务失败: ${data.message}`);
                    updateTaskStatusBadge('失败', 'danger');
                    hideTaskSpinner();

                    // 添加日志条目
                    addTaskLogEntry('启动失败', `任务启动失败: ${data.message}`, 'danger');

                    // 拒绝Promise
                    reject(new Error(data.message));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                updateTaskAlert('错误', 'danger');
                updateTaskMessage(`请求错误: ${error.message}`);
                updateTaskStatusBadge('错误', 'danger');
                hideTaskSpinner();

                // 添加日志条目
                addTaskLogEntry('系统错误', `请求错误: ${error.message}`, 'danger');

                // 拒绝Promise
                reject(error);
            });
        });
    }

    // 查询任务状态
    function checkTaskStatus() {
        fetch('/api/tasks/status')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateTaskStatus(data.status);

                // 如果任务已完成，停止查询
                if (!data.status.is_running) {
                    taskCompleted(data.status);
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            clearInterval(taskStatusInterval);
            hideTaskSpinner();
            updateTaskMessage(`获取状态失败: ${error.message}`);
            updateTaskAlert('错误', 'danger');

            // 添加日志条目
            addTaskLogEntry('状态查询失败', `获取任务状态失败: ${error.message}`, 'danger');
        });
    }

    // 任务完成处理
    function taskCompleted(status) {
        clearInterval(taskStatusInterval);
        hideTaskSpinner();

        // 显示查看结果按钮
        document.getElementById('view-results-btn').style.display = 'block';

        // 更新状态警报
        if (status.status === 'completed') {
            updateTaskAlert('完成', 'success');
            addTaskLogEntry('任务完成', `任务已成功完成，处理了 ${status.total_posts} 条内容，发现 ${status.relevant_posts} 条相关内容`, 'success');
        } else {
            updateTaskAlert('失败', 'danger');
            addTaskLogEntry('任务失败', `任务执行失败: ${status.message}`, 'danger');
        }

        // 更新进度条
        const progressBar = document.getElementById('task-progress');
        if (progressBar) {
            progressBar.style.width = '100%';
            if (status.status === 'completed') {
                progressBar.className = 'progress-bar bg-success';
            } else {
                progressBar.className = 'progress-bar bg-danger';
            }
        }
    }

    // 更新任务状态显示
    function updateTaskStatus(status) {
        if (!status) return;

        // 更新消息
        updateTaskMessage(status.message);

        // 更新状态徽章
        updateTaskStatusBadge(status.status);

        // 更新计数
        document.getElementById('task-posts-count').textContent = status.total_posts || 0;
        document.getElementById('task-relevant-count').textContent = status.relevant_posts || 0;

        // 更新时间信息
        if (status.start_time) {
            const startTime = new Date(status.start_time * 1000);
            document.getElementById('task-start-time').textContent = startTime.toLocaleString();

            // 计算运行时间
            const now = status.is_running ? new Date() : new Date(status.end_time * 1000);
            const durationSec = Math.floor((now - startTime) / 1000);
            document.getElementById('task-duration').textContent = formatDuration(durationSec);

            // 更新进度条
            if (status.progress && status.progress > 0) {
                const progressBar = document.getElementById('task-progress');
                if (progressBar) {
                    progressBar.style.width = `${Math.min(status.progress, 100)}%`;
                }
            }
        }

        // 如果有新消息，添加到日志
        if (status.message && status.message !== document.getElementById('task-message').textContent) {
            addTaskLogEntry('状态更新', status.message);
        }
    }

    // 更新任务消息
    function updateTaskMessage(message) {
        const messageEl = document.getElementById('task-message');
        if (messageEl && message) {
            messageEl.textContent = message;
        }
    }

    // 更新任务状态徽章
    function updateTaskStatusBadge(status, forceClass = null) {
        const statusText = document.getElementById('task-status-text');
        if (!statusText) return;

        statusText.textContent = status;

        // 设置徽章样式
        let badgeClass = 'bg-info';

        if (forceClass) {
            badgeClass = `bg-${forceClass}`;
        } else if (status === 'running') {
            badgeClass = 'bg-primary';
        } else if (status === 'completed') {
            badgeClass = 'bg-success';
        } else if (status === 'failed') {
            badgeClass = 'bg-danger';
        }

        statusText.className = `badge ${badgeClass}`;
    }

    // 更新任务警报
    function updateTaskAlert(title, type) {
        const alertEl = document.getElementById('task-status-alert');
        const titleEl = alertEl.querySelector('.alert-heading');

        if (alertEl && titleEl) {
            alertEl.className = `alert alert-${type} d-flex align-items-center mb-3`;
            titleEl.textContent = `任务${title}`;
        }
    }

    // 隐藏任务加载动画
    function hideTaskSpinner() {
        const spinner = document.getElementById('task-spinner');
        if (spinner) {
            spinner.style.display = 'none';
        }
    }

    // 添加任务日志条目
    function addTaskLogEntry(title, message, type = 'info') {
        const logContainer = document.getElementById('task-log');
        if (!logContainer) return;

        // 创建日志条目
        const entry = document.createElement('div');
        entry.className = 'list-group-item';

        // 设置条目样式
        if (type === 'danger') {
            entry.classList.add('list-group-item-danger');
        } else if (type === 'success') {
            entry.classList.add('list-group-item-success');
        } else if (type === 'warning') {
            entry.classList.add('list-group-item-warning');
        }

        // 设置条目内容
        const now = new Date();
        entry.innerHTML = `
            <div class="d-flex w-100 justify-content-between">
                <h6 class="mb-1">${title}</h6>
                <small class="text-muted">${now.toLocaleTimeString()}</small>
            </div>
            <p class="mb-1">${message}</p>
        `;

        // 添加到日志容器的顶部
        logContainer.insertBefore(entry, logContainer.firstChild);
    }

    // 格式化持续时间
    function formatDuration(seconds) {
        if (seconds < 60) {
            return `${seconds}秒`;
        } else if (seconds < 3600) {
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            return `${minutes}分${remainingSeconds}秒`;
        } else {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            return `${hours}小时${minutes}分`;
        }
    }

    // 重置模态框状态
    function resetTaskModal() {
        // 清除之前的定时器
        if (taskStatusInterval) {
            clearInterval(taskStatusInterval);
        }

        // 重置显示
        document.getElementById('task-spinner').style.display = 'inline-block';
        document.getElementById('task-message').textContent = '正在准备任务...';
        updateTaskStatusBadge('准备中', 'info');
        document.getElementById('task-posts-count').textContent = '0';
        document.getElementById('task-relevant-count').textContent = '0';
        document.getElementById('task-start-time').textContent = '-';
        document.getElementById('task-duration').textContent = '0秒';
        document.getElementById('view-results-btn').style.display = 'none';

        // 重置进度条
        const progressBar = document.getElementById('task-progress');
        const progressContainer = document.getElementById('task-progress-container');
        if (progressBar && progressContainer) {
            progressBar.style.width = '0%';
            progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated';
            progressContainer.style.display = 'none';
        }

        // 重置警报
        updateTaskAlert('进行中', 'primary');

        // 重置日志
        const logContainer = document.getElementById('task-log');
        if (logContainer) {
            logContainer.innerHTML = `
                <div class="list-group-item">
                    <div class="d-flex w-100 justify-content-between">
                        <h6 class="mb-1">任务初始化</h6>
                        <small class="text-muted">${new Date().toLocaleTimeString()}</small>
                    </div>
                    <p class="mb-1">系统正在准备执行监控任务...</p>
                </div>
            `;
        }
    }

    // 添加CSS动画
    const style = document.createElement('style');
    style.textContent = `
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .rotate-animation {
            animation: rotate 1s linear;
        }
        .step-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
        }
        .step-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #e9ecef;
            color: #6c757d;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        .step-item.active .step-circle {
            background-color: #007bff;
            color: white;
        }
        .step-item.completed .step-circle {
            background-color: #28a745;
            color: white;
        }
        .step-label {
            font-size: 0.875rem;
            color: #6c757d;
        }
        .step-item.active .step-label {
            color: #007bff;
            font-weight: 500;
        }
    `;
    document.head.appendChild(style);

    // 新增功能：全局搜索
    let searchTimeout;
    const globalSearch = document.getElementById('global-search');
    if (globalSearch) {
        globalSearch.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                performGlobalSearch(this.value);
            }, 300);
        });
    }

    // 清除搜索按钮
    const clearSearchBtn = document.getElementById('clear-search-btn');
    if (clearSearchBtn) {
        clearSearchBtn.addEventListener('click', function() {
            globalSearch.value = '';
            performGlobalSearch('');
        });
    }

    // 执行全局搜索
    function performGlobalSearch(query) {
        const rows = document.querySelectorAll('.account-row');
        let visibleCount = 0;

        rows.forEach(row => {
            const accountId = row.getAttribute('data-account-id') || '';
            const displayName = row.querySelector('.user-name-info .fw-medium')?.textContent || '';
            const tag = row.getAttribute('data-tag') || '';
            const platform = row.getAttribute('data-platform') || '';

            const searchText = `${accountId} ${displayName} ${tag} ${platform}`.toLowerCase();
            const isVisible = !query || searchText.includes(query.toLowerCase());

            if (isVisible) {
                row.style.display = '';
                visibleCount++;
                // 高亮搜索结果
                highlightSearchText(row, query);
            } else {
                row.style.display = 'none';
            }
        });

        // 更新统计
        updateAccountStats();
    }

    // 高亮搜索文本
    function highlightSearchText(row, query) {
        if (!query) {
            // 清除之前的高亮
            const highlightedElements = row.querySelectorAll('mark.search-highlight');
            highlightedElements.forEach(mark => {
                const parent = mark.parentNode;
                parent.replaceChild(document.createTextNode(mark.textContent), mark);
                parent.normalize();
            });
            return;
        }

        const textElements = row.querySelectorAll('.fw-medium, .text-muted:not(.ms-2)');
        textElements.forEach(el => {
            // 先清除之前的高亮
            const existingMarks = el.querySelectorAll('mark.search-highlight');
            existingMarks.forEach(mark => {
                const parent = mark.parentNode;
                parent.replaceChild(document.createTextNode(mark.textContent), mark);
            });
            el.normalize();

            const text = el.textContent;
            if (text.toLowerCase().includes(query.toLowerCase())) {
                const regex = new RegExp(`(${query})`, 'gi');
                el.innerHTML = text.replace(regex, '<mark class="search-highlight">$1</mark>');
            }
        });
    }

    // 快速筛选功能
    document.querySelectorAll('[data-filter]').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const filter = this.getAttribute('data-filter');
            applyQuickFilter(filter);
        });
    });

    // 应用快速筛选
    function applyQuickFilter(filter) {
        const rows = document.querySelectorAll('.account-row');

        if (filter === 'clear') {
            rows.forEach(row => row.style.display = '');
            updateAccountStats();
            return;
        }

        rows.forEach(row => {
            let isVisible = false;

            switch (filter) {
                case 'verified':
                    isVisible = row.getAttribute('data-verified') === 'true';
                    break;
                case 'auto-reply':
                    isVisible = row.getAttribute('data-auto-reply') === 'enabled';
                    break;
                case 'ai-bypass':
                    isVisible = row.getAttribute('data-ai') === 'disabled';
                    break;
                case 'twitter':
                    isVisible = row.getAttribute('data-platform') === 'twitter';
                    break;
                case 'weibo':
                    isVisible = row.getAttribute('data-platform') === 'weibo';
                    break;
            }

            row.style.display = isVisible ? '' : 'none';
        });

        updateAccountStats();
    }

    // 更新账号统计
    function updateAccountStats() {
        const allRows = document.querySelectorAll('.account-row');

        // 获取可见的行（没有被筛选隐藏的）
        const visibleRows = Array.from(allRows).filter(row => {
            const style = row.style.display;
            return style !== 'none';
        });

        // 计算各种统计数据
        const autoReplyRows = visibleRows.filter(row =>
            row.getAttribute('data-auto-reply') === 'enabled'
        );

        // 需要关注的账号：AI分析被禁用或未验证的账号
        const needsAttentionRows = visibleRows.filter(row =>
            row.getAttribute('data-ai') === 'disabled' ||
            row.getAttribute('data-verified') === 'false'
        );

        // 活跃账号：这里可以根据实际需求定义，暂时使用可见账号数
        const activeRows = visibleRows.filter(row =>
            row.getAttribute('data-auto-reply') === 'enabled' ||
            row.getAttribute('data-verified') === 'true'
        );

        // 更新统计数字
        const statsElements = {
            'stats-total-accounts': visibleRows.length,
            'stats-active-accounts': activeRows.length,
            'stats-auto-reply-accounts': autoReplyRows.length,
            'stats-needs-attention': needsAttentionRows.length
        };

        // 安全更新DOM元素
        Object.entries(statsElements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });

        // 同时更新页面底部的总账号数
        const totalAccountsElement = document.getElementById('total-accounts');
        if (totalAccountsElement) {
            totalAccountsElement.textContent = visibleRows.length;
        }
    }

    // 批量选择功能
    const selectAllCheckbox = document.getElementById('select-all-accounts');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.account-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkActions();
        });
    }

    // 监听单个复选框变化
    document.querySelectorAll('.account-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });

    // 更新批量操作工具栏
    function updateBulkActions() {
        const selectedCheckboxes = document.querySelectorAll('.account-checkbox:checked');
        const bulkActions = document.getElementById('bulk-actions');
        const selectedCount = document.getElementById('selected-count');

        if (selectedCheckboxes.length > 0) {
            bulkActions.style.display = 'block';
            selectedCount.textContent = selectedCheckboxes.length;

            // 更新选中行的样式
            document.querySelectorAll('.account-row').forEach(row => {
                const checkbox = row.querySelector('.account-checkbox');
                if (checkbox && checkbox.checked) {
                    row.classList.add('selected');
                } else {
                    row.classList.remove('selected');
                }
            });
        } else {
            bulkActions.style.display = 'none';
            document.querySelectorAll('.account-row').forEach(row => {
                row.classList.remove('selected');
            });
        }
    }

    // 取消选择按钮
    const cancelSelectionBtn = document.getElementById('cancel-selection-btn');
    if (cancelSelectionBtn) {
        cancelSelectionBtn.addEventListener('click', function() {
            document.querySelectorAll('.account-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });
            selectAllCheckbox.checked = false;
            updateBulkActions();
        });
    }

    // 批量抓取按钮
    const bulkCrawlBtn = document.getElementById('bulk-crawl-btn');
    if (bulkCrawlBtn) {
        bulkCrawlBtn.addEventListener('click', function() {
            const selectedIds = Array.from(document.querySelectorAll('.account-checkbox:checked'))
                .map(cb => cb.value);

            if (selectedIds.length === 0) {
                showToast('提示', '请先选择要抓取的账号', 'warning');
                return;
            }

            // 执行批量抓取
            bulkCrawlAccounts(selectedIds);
        });
    }

    // 批量标签按钮
    const bulkTagBtn = document.getElementById('bulk-tag-btn');
    if (bulkTagBtn) {
        bulkTagBtn.addEventListener('click', function() {
            const selectedCheckboxes = document.querySelectorAll('.account-checkbox:checked');

            if (selectedCheckboxes.length === 0) {
                showToast('提示', '请先选择要设置标签的账号', 'warning');
                return;
            }

            // 显示批量标签模态框
            showBulkTagModal(selectedCheckboxes);
        });
    }

    // 批量删除按钮
    const bulkDeleteBtn = document.getElementById('bulk-delete-btn');
    if (bulkDeleteBtn) {
        bulkDeleteBtn.addEventListener('click', function() {
            const selectedCheckboxes = document.querySelectorAll('.account-checkbox:checked');

            if (selectedCheckboxes.length === 0) {
                showToast('提示', '请先选择要删除的账号', 'warning');
                return;
            }

            // 确认批量删除
            if (confirm(`确定要删除选中的 ${selectedCheckboxes.length} 个账号吗？此操作不可撤销。`)) {
                bulkDeleteAccounts(selectedCheckboxes);
            }
        });
    }

    // 初始化统计
    updateAccountStats();

    // 批量抓取账号
    function bulkCrawlAccounts(accountIds) {
        showToast('信息', `开始批量抓取 ${accountIds.length} 个账号...`, 'info');

        // 这里可以调用批量抓取API
        fetch('/api/accounts/bulk-crawl', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]')?.getAttribute('content')
            },
            body: JSON.stringify({ account_ids: accountIds })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('成功', `成功启动 ${accountIds.length} 个账号的抓取任务`, 'success');
                // 取消选择
                document.getElementById('cancel-selection-btn').click();
            } else {
                showToast('错误', `批量抓取失败: ${data.message}`, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('错误', '批量抓取请求失败', 'danger');
        });
    }

    // 显示批量标签模态框
    function showBulkTagModal(selectedCheckboxes) {
        const modal = new bootstrap.Modal(document.getElementById('bulkTagModal'));
        const accountsList = document.getElementById('selected-accounts-list');

        // 清空并填充选中的账号列表
        accountsList.innerHTML = '';
        selectedCheckboxes.forEach(checkbox => {
            const row = checkbox.closest('.account-row');
            const accountId = row.getAttribute('data-account-id');
            const platform = row.getAttribute('data-platform');

            const accountItem = document.createElement('div');
            accountItem.className = 'mb-1';
            accountItem.innerHTML = `
                <span class="badge bg-${platform === 'twitter' ? 'primary' : 'danger'} me-2">
                    <i class="bi bi-${platform === 'twitter' ? 'twitter' : 'sina-weibo'} me-1"></i>${platform}
                </span>
                ${accountId}
            `;
            accountsList.appendChild(accountItem);
        });

        modal.show();
    }

    // 批量标签确认按钮
    const bulkTagConfirmBtn = document.getElementById('bulk-tag-confirm-btn');
    if (bulkTagConfirmBtn) {
        bulkTagConfirmBtn.addEventListener('click', function() {
            const tagInput = document.getElementById('bulk-tag-input');
            const tag = tagInput.value.trim();

            if (!tag) {
                showToast('提示', '请输入标签名称', 'warning');
                return;
            }

            const selectedIds = Array.from(document.querySelectorAll('.account-checkbox:checked'))
                .map(cb => cb.value);

            // 执行批量设置标签
            bulkSetTags(selectedIds, tag);
        });
    }

    // 批量设置标签
    function bulkSetTags(accountIds, tag) {
        fetch('/api/accounts/bulk-tag', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]')?.getAttribute('content')
            },
            body: JSON.stringify({
                account_ids: accountIds,
                tag: tag
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('成功', `成功为 ${accountIds.length} 个账号设置标签`, 'success');

                // 关闭模态框
                bootstrap.Modal.getInstance(document.getElementById('bulkTagModal')).hide();

                // 刷新页面或更新显示
                location.reload();
            } else {
                showToast('错误', `批量设置标签失败: ${data.message}`, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('错误', '批量设置标签请求失败', 'danger');
        });
    }

    // 批量删除账号
    function bulkDeleteAccounts(selectedCheckboxes) {
        const accountIds = Array.from(selectedCheckboxes).map(cb => cb.value);

        fetch('/api/accounts/bulk-delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]')?.getAttribute('content')
            },
            body: JSON.stringify({ account_ids: accountIds })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('成功', `成功删除 ${accountIds.length} 个账号`, 'success');

                // 移除已删除的行
                selectedCheckboxes.forEach(checkbox => {
                    const row = checkbox.closest('.account-row');
                    row.remove();
                });

                // 更新统计
                updateAccountStats();

                // 隐藏批量操作工具栏
                document.getElementById('bulk-actions').style.display = 'none';
            } else {
                showToast('错误', `批量删除失败: ${data.message}`, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('错误', '批量删除请求失败', 'danger');
        });
    }

    // 导出账号列表
    const exportAccountsBtn = document.getElementById('export-accounts-btn');
    if (exportAccountsBtn) {
        exportAccountsBtn.addEventListener('click', function(e) {
            e.preventDefault();

            // 获取当前可见的账号
            const allRows = document.querySelectorAll('.account-row');
            const visibleRows = Array.from(allRows).filter(row => {
                const style = row.style.display;
                return style !== 'none';
            });
            const accountData = visibleRows.map(row => {
                // 获取用户名信息
                const userNameElement = row.querySelector('.user-name-info .fw-medium');
                const displayName = userNameElement ? userNameElement.textContent.trim() : '';

                // 获取简介信息
                const bioElement = row.querySelector('.user-name-info small');
                const bio = bioElement ? bioElement.getAttribute('title') || bioElement.textContent.trim() : '';

                // 获取粉丝数信息
                const followersElement = row.querySelector('.bi-people');
                const followersCount = followersElement ? followersElement.parentElement.textContent.replace(/[^\d]/g, '') : '';

                // 获取创建时间
                const createdTimestamp = row.getAttribute('data-created');
                const createdDate = createdTimestamp ? new Date(parseFloat(createdTimestamp) * 1000).toLocaleString('zh-CN') : '';

                // 获取验证状态
                const verified = row.getAttribute('data-verified') === 'true';

                return {
                    platform: row.getAttribute('data-platform'),
                    account_id: row.getAttribute('data-account-id'),
                    display_name: displayName,
                    bio: bio,
                    followers_count: followersCount,
                    tag: row.getAttribute('data-tag') || '',
                    auto_reply: row.getAttribute('data-auto-reply') === 'enabled',
                    ai_analysis: row.getAttribute('data-ai') === 'enabled',
                    verified: verified,
                    created_at: createdDate
                };
            });

            // 创建CSV内容
            const csvContent = generateCSV(accountData);

            // 下载文件
            downloadFile(csvContent, 'accounts.csv', 'text/csv');

            showToast('成功', '账号列表已导出', 'success');
        });
    }

    // 生成CSV内容
    function generateCSV(data) {
        const headers = [
            '平台',
            '账号ID',
            '显示名称',
            '简介',
            '粉丝数',
            '标签',
            '自动回复',
            'AI分析',
            '已验证',
            '创建时间'
        ];

        const rows = data.map(item => [
            item.platform,
            item.account_id,
            item.display_name || item.account_id,
            item.bio || '',
            item.followers_count || '',
            item.tag || '',
            item.auto_reply ? '是' : '否',
            item.ai_analysis ? '是' : '否',
            item.verified ? '是' : '否',
            item.created_at || ''
        ]);

        const csvContent = [headers, ...rows]
            .map(row => row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(','))
            .join('\n');

        return '\uFEFF' + csvContent; // 添加BOM以支持中文
    }

    // 下载文件
    function downloadFile(content, filename, contentType) {
        const blob = new Blob([content], { type: contentType });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
    }

    // 显示Toast通知
    function showToast(title, message, type = 'info') {
        // 创建toast容器（如果不存在）
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            toastContainer.style.zIndex = '9999';
            document.body.appendChild(toastContainer);
        }

        // 创建toast元素
        const toastId = 'toast-' + Date.now();
        const toastHtml = `
            <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header">
                    <i class="bi bi-${getToastIcon(type)} text-${type} me-2"></i>
                    <strong class="me-auto">${title}</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;

        toastContainer.insertAdjacentHTML('beforeend', toastHtml);

        // 显示toast
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, {
            autohide: true,
            delay: 5000
        });
        toast.show();

        // 自动移除toast元素
        toastElement.addEventListener('hidden.bs.toast', function() {
            this.remove();
        });
    }

    // 获取Toast图标
    function getToastIcon(type) {
        const icons = {
            'success': 'check-circle-fill',
            'danger': 'exclamation-triangle-fill',
            'warning': 'exclamation-triangle-fill',
            'info': 'info-circle-fill'
        };
        return icons[type] || 'info-circle-fill';
    }
</script>
{% endblock %}