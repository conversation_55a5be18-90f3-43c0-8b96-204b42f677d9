templates:
  finance: |
    你现在是一名财经专家，请对以下财经博主的发言进行分析，并给按我指定的格式返回分析结果。
    这是你需要分析的内容：{content}
    这是输出格式的说明： {
        "is_relevant": "是否与财经相关，且与美股市场或美债市场或科技股或半导体股或中国股票市场或香港股票市场或人民币兑美元汇率或中美关系相关。如果相关就返回1，如果不相关就返回0。只需要返回1或0这两个值之一即可",
        "analytical_briefing": "分析简报"
    }
    其中analytical_briefing的值是一个字符串，它是针对内容所做的分析简报，仅在is_relevant为1时会返回这个值。
    analytical_briefing的内容是markdown格式的，它需要符合下面的规范
    原始正文，仅当需要分析的内容不是为中文时，这部分内容才会保留，否则这部分的内容为原始的正文
    翻译后的内容，仅当需要分析的内容为英文时，才会有这部分的内容。
    ## Brief Analysis
    分析结果。这部分会展示一个列表，列表中分别包含美股市场、美债市场、科技股、半导体股、中国股票市场、香港股票市场、人民币兑美元汇率、中美关系这8个选项。 每个选项的值为分别为📈利多和📉利空。如果分析内容对于该选项没有影响，就不要针对这个选项返回任何内容。
    ## Summarize
    这部分需要用非常简明扼要的文字对分析结果进行总结，以及解释为什么在上面针对不同选项会得出不同的结论。

  tech: |
    你现在是一名科技专家，请对以下科技博主的发言进行分析，并给按我指定的格式返回分析结果。
    这是你需要分析的内容：{content}
    这是输出格式的说明： {
        "is_relevant": "是否与科技相关，且与人工智能、大语言模型、芯片、半导体、云计算、区块链、元宇宙或Web3相关。如果相关就返回1，如果不相关就返回0。只需要返回1或0这两个值之一即可",
        "analytical_briefing": "分析简报"
    }
    其中analytical_briefing的值是一个字符串，它是针对内容所做的分析简报，仅在is_relevant为1时会返回这个值。
    analytical_briefing的内容是markdown格式的，它需要符合下面的规范
    原始正文，仅当需要分析的内容不是为中文时，这部分内容才会保留，否则这部分的内容为原始的正文
    翻译后的内容，仅当需要分析的内容为英文时，才会有这部分的内容。
    ## Brief Analysis
    分析结果。这部分会展示一个列表，列表中分别包含人工智能、大语言模型、芯片、半导体、云计算、区块链、元宇宙、Web3这8个选项。 每个选项的值为分别为📈看好和📉看淡。如果分析内容对于该选项没有影响，就不要针对这个选项返回任何内容。
    ## Summarize
    这部分需要用非常简明扼要的文字对分析结果进行总结，以及解释为什么在上面针对不同选项会得出不同的结论。

  general: |
    请分析以下社交媒体内容，判断其是否与我们关注的主题相关。
    主题：人工智能、机器学习、深度学习、神经网络、大语言模型等AI相关技术和应用。

    内容：{content}

    请按照以下JSON格式回复：
    {
      "is_relevant": true/false,  // 是否相关
      "confidence": 0-100,  // 置信度，0-100的整数
      "reason": "判断理由",  // 简要说明判断理由
      "keywords": ["关键词1", "关键词2"]  // 内容中的相关关键词
    }
