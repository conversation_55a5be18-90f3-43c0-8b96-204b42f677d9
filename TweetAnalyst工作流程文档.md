# TweetAnalyst 工作流程文档

## 1. 系统架构概述

TweetAnalyst 是一个社交媒体内容分析工具，主要用于监控和分析社交媒体账号上的内容，判断内容是否与特定主题相关，并可以自动回复和推送通知。系统采用模块化设计，主要包括以下几个核心组件：

1. **Web应用层**：基于Flask的Web界面，提供用户交互和系统管理功能
2. **数据层**：使用SQLite数据库存储系统配置、账号信息和分析结果
3. **服务层**：提供配置管理、状态存储、推送队列等服务
4. **AI分析层**：使用LangChain与各种LLM API交互，进行内容分析
5. **社交媒体层**：与社交媒体平台交互，获取内容和发送回复
6. **推送通知层**：通过Apprise库支持多种推送渠道

## 2. 核心工作流程

### 2.1 内容监控与分析流程

```
获取社交媒体内容 → 内容预处理 → AI分析 → 决策（是否推送）→ 推送通知 → 可选自动回复
```

1. **获取社交媒体内容**：
   - 系统定期从配置的社交媒体账号获取最新内容
   - 支持手动触发和定时任务两种方式

2. **内容预处理**：
   - 检查账号是否设置了`bypass_ai`（绕过AI判断）
   - 如果设置了`bypass_ai=true`，则直接推送内容，不经过AI判断
   - 如果设置了`bypass_ai=false`（默认），则进入AI判断流程

3. **AI分析**：
   - 使用账号指定的AI提供商（或默认提供商）分析内容
   - 根据账号配置的提示词模板生成分析提示
   - 调用LLM API进行内容分析
   - 解析AI返回的结果，提取关键信息（是否推送、置信度、理由、摘要）

4. **决策（是否推送）**：
   - 根据AI分析结果决定是否推送内容
   - 如果AI决定推送，则进入推送流程
   - 如果AI决定不推送，则记录结果但不推送

5. **推送通知**：
   - 构建推送消息，包含原始内容、分析结果和推送理由
   - 根据账号标签（tag）选择推送目标
   - 将通知加入推送队列
   - 推送队列服务处理队列中的通知，发送到配置的推送渠道

6. **可选自动回复**：
   - 如果账号启用了自动回复功能，则生成回复内容
   - 使用社交媒体API发送回复

### 2.2 AI提供商管理流程

```
添加AI提供商 → 配置账号使用的提供商 → AI轮询服务健康检查 → 智能负载均衡
```

1. **添加AI提供商**：
   - 在Web界面添加AI提供商，配置API密钥、基础URL、模型名称等
   - 支持多种提供商类型（OpenAI、X.AI、Groq等）
   - 可以设置提供商优先级和支持的媒体类型

2. **配置账号使用的提供商**：
   - 为每个社交媒体账号指定默认AI提供商
   - 可以根据内容类型（文本、图片、视频、GIF）指定不同的提供商

3. **AI轮询服务健康检查**：
   - AI轮询服务定期检查所有提供商的健康状态
   - 记录提供商的成功率、响应时间等指标
   - 更新提供商的健康分数

4. **智能负载均衡**：
   - 根据提供商的健康分数、优先级和负载情况选择最合适的提供商
   - 支持请求缓存，减少重复请求
   - 支持批处理请求，提高效率

### 2.3 推送通知流程

```
构建推送消息 → 加入推送队列 → 处理推送队列 → 发送通知 → 记录结果
```

1. **构建推送消息**：
   - 根据内容类型和分析结果构建推送消息
   - 支持Markdown格式，包含原始内容、分析结果和推送理由

2. **加入推送队列**：
   - 将推送消息加入推送队列
   - 记录推送目标、账号ID、帖子ID等元数据

3. **处理推送队列**：
   - 推送队列服务定期处理队列中的通知
   - 支持失败重试、定时发送等高级功能

4. **发送通知**：
   - 根据配置的推送渠道发送通知
   - 支持多种推送渠道（Telegram、Discord、Slack、企业微信等）

5. **记录结果**：
   - 记录推送结果（成功/失败）
   - 更新推送通知的状态

## 3. 关键文件及其作用

### 3.1 核心应用文件

| 文件 | 作用 |
|------|------|
| `web_app.py` | Web应用程序主文件，定义了路由和核心功能 |
| `main.py` | 主程序入口，处理社交媒体内容的获取和分析 |
| `run_web.py` | Web应用启动脚本 |
| `run_scheduler.py` | 定时任务启动脚本 |
| `run_all.py` | 同时启动Web应用和定时任务的脚本 |
| `run_push_queue.py` | 推送队列处理脚本 |

### 3.2 数据模型文件

| 文件 | 作用 |
|------|------|
| `models/__init__.py` | 数据库初始化和模型导入 |
| `models/user.py` | 用户模型，用于系统登录和权限控制 |
| `models/social_account.py` | 社交媒体账号模型，存储监控的账号信息 |
| `models/analysis_result.py` | 分析结果模型，存储内容分析结果 |
| `models/ai_provider.py` | AI提供商模型，管理不同的AI服务提供商 |
| `models/ai_request_log.py` | AI请求日志模型，记录AI请求的详细信息 |
| `models/system_config.py` | 系统配置模型，存储系统配置信息 |
| `models/system_state.py` | 系统状态模型，存储系统状态信息 |
| `models/notification_service.py` | 通知服务模型，存储通知服务配置 |
| `models/push_notification.py` | 推送通知模型，存储推送通知信息 |

### 3.3 服务层文件

| 文件 | 作用 |
|------|------|
| `services/config_service.py` | 配置服务，提供系统配置的管理功能 |
| `services/state_service.py` | 状态服务，提供系统状态的管理功能 |
| `services/ai_polling_service.py` | AI轮询服务，管理AI提供商的健康状态和负载均衡 |
| `services/push_queue_service.py` | 推送队列服务，管理推送通知队列 |
| `services/push_queue_worker.py` | 推送队列工作线程，处理推送队列中的通知 |
| `services/repository/factory.py` | 仓储工厂，创建各种仓储实例 |
| `services/repository/base_repository.py` | 基础仓储类，提供通用的数据库操作 |
| `services/repository/system_config_repository.py` | 系统配置仓储，提供系统配置的数据库操作 |
| `services/repository/system_state_repository.py` | 系统状态仓储，提供系统状态的数据库操作 |
| `services/repository/social_account_repository.py` | 社交账号仓储，提供社交账号的数据库操作 |
| `services/repository/analysis_result_repository.py` | 分析结果仓储，提供分析结果的数据库操作 |
| `services/test_service.py` | 测试服务，提供系统测试功能 |

### 3.4 API接口文件

| 文件 | 作用 |
|------|------|
| `api/__init__.py` | API蓝图初始化 |
| `api/ai_provider.py` | AI提供商API，提供AI提供商的CRUD操作 |
| `api/accounts.py` | 社交账号API，提供社交账号的CRUD操作 |
| `api/analytics.py` | 数据分析API，提供数据分析和统计功能 |
| `api/settings.py` | 系统设置API，提供系统配置管理功能 |
| `api/logs.py` | 日志API，提供系统日志查询功能 |
| `api/notifications.py` | 通知API，提供通知服务管理功能 |
| `api/tasks.py` | 任务API，提供任务管理功能 |
| `api/test.py` | 测试API，提供系统测试功能 |

### 3.5 路由文件

| 文件 | 作用 |
|------|------|
| `routes/push_notifications.py` | 推送通知路由，提供推送通知页面和API |
| `routes/ai_settings.py` | AI设置路由，提供AI设置页面和API |

### 3.6 模块文件

| 文件 | 作用 |
|------|------|
| `modules/socialmedia/twitter.py` | Twitter模块，提供Twitter API的封装和交互功能 |
| `modules/langchain/llm.py` | LLM模块，提供大型语言模型的封装和交互功能 |
| `modules/bots/apprise_adapter.py` | 通知适配器，提供通知功能的封装和交互 |

### 3.7 工具文件

| 文件 | 作用 |
|------|------|
| `utils/logger.py` | 日志工具，提供日志记录功能 |
| `utils/api_utils.py` | API工具，提供API请求和响应处理功能 |
| `utils/api_decorators.py` | API装饰器，提供API错误处理和缓存功能 |
| `utils/yaml_utils.py` | YAML工具，提供YAML文件的读写功能 |
| `utils/test_utils.py` | 测试工具，提供系统测试功能 |
| `utils/prompts/default_prompts.py` | 默认提示词模板，提供各种领域的默认提示词 |

### 3.8 数据库迁移文件

| 文件 | 作用 |
|------|------|
| `migrations/db_migrations.py` | 数据库迁移脚本，提供数据库结构的升级和迁移功能 |
| `migrations/add_ai_provider_fields.py` | 添加AI提供商字段的迁移脚本 |
| `migrations/add_ai_request_logs.py` | 添加AI请求日志表的迁移脚本 |
| `migrations/add_confidence_reason_fields.py` | 添加置信度和理由字段的迁移脚本 |
| `migrations/add_notification_services.py` | 添加通知服务表的迁移脚本 |
| `migrations/add_bypass_ai_field.py` | 添加绕过AI字段的迁移脚本 |
| `migrations/add_avatar_url_field.py` | 添加头像URL字段的迁移脚本 |
| `migrations/add_account_details_fields.py` | 添加账号详细信息字段的迁移脚本 |

## 4. 组件交互详解

### 4.1 账号与AI提供商交互

系统支持为不同账号指定不同的AI提供商，实现灵活的AI分析策略：

1. **账号-提供商关联**：
   - `SocialAccount`模型中有多个AI提供商相关字段：
     - `ai_provider_id`：默认AI提供商ID
     - `text_provider_id`：文本内容AI提供商ID
     - `image_provider_id`：图片内容AI提供商ID
     - `video_provider_id`：视频内容AI提供商ID
     - `gif_provider_id`：GIF内容AI提供商ID

2. **提供商选择逻辑**：
   - 系统首先检查内容类型（文本、图片、视频、GIF）
   - 根据内容类型查找对应的专用提供商（如`text_provider_id`）
   - 如果没有专用提供商，则使用默认提供商（`ai_provider_id`）
   - 如果账号没有设置任何提供商，则使用系统级默认提供商

3. **提供商健康检查**：
   - AI轮询服务定期检查所有提供商的健康状态
   - 记录成功率、响应时间等指标
   - 计算健康分数，用于智能负载均衡

4. **智能负载均衡**：
   - 根据健康分数、优先级和使用时间选择最合适的提供商
   - 优先使用健康状态良好、优先级高的提供商
   - 避免所有请求都集中到单个提供商

### 4.2 代理组件与其他组件交互

代理组件可以与所有需要网络访问的组件交互，确保系统在各种网络环境下都能正常工作：

1. **代理配置**：
   - 代理设置存储在环境变量`HTTP_PROXY`和`HTTPS_PROXY`中
   - 可以通过Web界面配置代理设置
   - 支持HTTP和SOCKS代理

2. **代理应用范围**：
   - AI API请求：与OpenAI、X.AI等API交互时使用代理
   - 社交媒体API请求：与Twitter等平台交互时使用代理
   - 推送通知请求：发送Telegram等通知时使用代理

3. **代理自动检测**：
   - 系统启动时自动检测代理类型
   - 对于SOCKS代理，自动安装必要的依赖
   - 提供代理测试功能，验证代理是否正常工作

4. **统一API请求函数**：
   - `api_request`函数自动应用代理设置
   - 处理代理相关的错误和异常
   - 提供重试机制，应对代理不稳定的情况

### 4.3 推送组件工作流程

推送组件负责将分析结果通过多种渠道推送给用户，支持灵活的推送策略：

1. **推送决策**：
   - 检查账号是否设置了`bypass_ai`（绕过AI判断）
   - 如果`bypass_ai=true`，则直接推送内容，不经过AI判断
   - 如果`bypass_ai=false`（默认），则根据AI分析结果决定是否推送

2. **推送消息构建**：
   - 构建Markdown格式的推送消息
   - 包含原始内容、分析结果和推送理由
   - 添加媒体内容（如图片、视频）的链接

3. **推送队列**：
   - 将推送消息加入推送队列
   - 记录推送目标、账号ID、帖子ID等元数据
   - 支持定时发送、失败重试等高级功能

4. **多渠道推送**：
   - 基于Apprise库支持多种推送渠道
   - 根据账号标签（tag）选择推送目标
   - 支持同时推送到多个渠道

5. **推送结果记录**：
   - 记录推送结果（成功/失败）
   - 更新推送通知的状态
   - 支持失败通知的重试

## 5. 配置说明

### 5.1 环境变量配置

系统支持通过环境变量进行配置，主要包括以下几类：

1. **基础配置**：
   - `FLASK_SECRET_KEY`：Web应用的密钥，用于会话安全
   - `DATABASE_PATH`：数据库文件路径
   - `FIRST_LOGIN`：首次登录标志（auto/true/false）

2. **LLM API配置**：
   - `LLM_API_KEY`：API密钥
   - `LLM_API_MODEL`：模型名称
   - `LLM_API_BASE`：API基础URL

3. **社交媒体配置**：
   - `TWITTER_USERNAME`：Twitter用户名
   - `TWITTER_PASSWORD`：Twitter密码
   - `TWITTER_SESSION`：Twitter会话数据

4. **推送配置**：
   - `APPRISE_URLS`：Apprise推送URL，支持多种推送方式

5. **网络配置**：
   - `HTTP_PROXY`：HTTP代理地址
   - `HTTPS_PROXY`：HTTPS代理地址

6. **定时任务配置**：
   - `SCHEDULER_INTERVAL_MINUTES`：定时任务执行间隔（分钟）

7. **日志配置**：
   - `LOG_DIR`：日志目录
   - `LOG_LEVEL`：日志级别

### 5.2 数据库配置

系统使用SQLite数据库存储数据，主要包括以下几个表：

1. **用户表**（`user`）：
   - 存储系统用户信息
   - 包含用户名、密码哈希、管理员标志等字段

2. **社交账号表**（`social_account`）：
   - 存储监控的社交媒体账号信息
   - 包含账号ID、平台类型、提示词模板、AI提供商ID等字段

3. **分析结果表**（`analysis_result`）：
   - 存储内容分析结果
   - 包含帖子ID、内容、分析结果、是否相关等字段

4. **AI提供商表**（`ai_provider`）：
   - 存储AI提供商信息
   - 包含API密钥、基础URL、模型名称、优先级等字段

5. **AI请求日志表**（`ai_request_logs`）：
   - 记录AI请求的详细信息
   - 包含请求类型、内容、响应、成功标志等字段

6. **系统配置表**（`system_config`）：
   - 存储系统配置信息
   - 包含配置键、值、描述、敏感标志等字段

7. **系统状态表**（`system_state`）：
   - 存储系统状态信息
   - 包含状态键、值、过期时间等字段

8. **通知服务表**（`notification_service`）：
   - 存储通知服务配置
   - 包含服务名称、URL、标签等字段

9. **推送通知表**（`push_notification`）：
   - 存储推送通知信息
   - 包含标题、内容、状态、尝试次数等字段

### 5.3 提示词模板配置

系统支持自定义提示词模板，用于指导AI分析内容：

1. **默认提示词模板**：
   - 系统提供多种领域的默认提示词模板
   - 包括通用、财经、AI/技术、新闻等领域

2. **账号级提示词模板**：
   - 每个社交账号可以配置自己的提示词模板
   - 优先使用账号级模板，如果未设置则使用默认模板

3. **提示词变量**：
   - `{content}`：原始内容
   - `{account_id}`：账号ID
   - `{account_type}`：账号类型
   - 其他自定义变量

4. **提示词格式**：
   - 使用JSON格式返回分析结果
   - 包含`should_push`、`confidence`、`reason`、`summary`等字段

## 6. 常见问题与解决方案

### 6.1 数据库问题

1. **数据库迁移失败**：
   - 检查数据库文件权限
   - 确保数据库目录存在并可写
   - 查看迁移日志，了解具体错误

2. **外键关系错误**：
   - 检查模型定义中的表名和外键引用是否一致
   - 使用迁移脚本修复外键关系
   - 确保所有模型都正确导入

### 6.2 AI API问题

1. **API密钥无效**：
   - 检查API密钥是否正确
   - 确认API密钥是否有足够的权限
   - 验证API密钥是否过期

2. **API限流**：
   - 使用多个API提供商分散请求
   - 实现请求缓存，减少重复请求
   - 添加指数退避重试机制

3. **API响应解析错误**：
   - 检查提示词模板是否正确
   - 确保API返回的格式符合预期
   - 添加更健壮的错误处理和回退机制

### 6.3 推送问题

1. **推送失败**：
   - 检查Apprise URL是否正确
   - 确认网络连接和代理设置
   - 查看推送日志，了解具体错误

2. **推送队列堆积**：
   - 增加推送队列处理频率
   - 检查推送服务是否正常运行
   - 清理过期的推送通知

### 6.4 社交媒体API问题

1. **登录失败**：
   - 检查用户名和密码是否正确
   - 确认账号是否需要验证码或二次验证
   - 使用会话数据代替用户名和密码

2. **API限制**：
   - 减少请求频率
   - 使用多个账号轮换请求
   - 实现请求缓存，减少重复请求

## 7. 开发指南

### 7.1 添加新功能

1. **添加新的社交媒体平台**：
   - 在`modules/socialmedia/`目录下创建新的模块
   - 实现获取内容和发送回复的功能
   - 更新`main.py`中的处理逻辑

2. **添加新的推送渠道**：
   - 确认Apprise是否已支持该渠道
   - 如果支持，直接在Web界面添加Apprise URL
   - 如果不支持，考虑扩展Apprise或实现自定义适配器

3. **添加新的AI提供商**：
   - 确认提供商是否兼容OpenAI API格式
   - 如果兼容，直接在Web界面添加提供商
   - 如果不兼容，需要实现自定义适配器

### 7.2 代码风格和最佳实践

1. **命名约定**：
   - 类名使用驼峰命名法（如`AIProvider`）
   - 函数和变量名使用下划线命名法（如`get_config`）
   - 常量使用大写下划线命名法（如`DEFAULT_PROMPT`）

2. **错误处理**：
   - 使用`try-except`块捕获和处理异常
   - 记录详细的错误日志
   - 提供用户友好的错误消息

3. **代码组织**：
   - 使用模块化设计，将相关功能组织在一起
   - 避免循环导入，使用延迟导入或依赖注入
   - 保持函数和类的单一职责

4. **测试**：
   - 编写单元测试，确保功能正常
   - 使用测试工具验证系统组件
   - 定期进行集成测试和端到端测试

### 7.3 贡献指南

1. **提交代码**：
   - 遵循项目的代码风格和命名约定
   - 提供详细的提交信息，说明修改内容和原因
   - 确保代码通过所有测试

2. **报告问题**：
   - 提供详细的问题描述和复现步骤
   - 附上相关的日志和错误信息
   - 说明系统环境和配置

3. **提出建议**：
   - 清晰描述建议的功能或改进
   - 解释建议的价值和使用场景
   - 如果可能，提供实现思路或原型
