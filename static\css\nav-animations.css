/**
 * TweetAnalyst导航栏动画效果
 * 为导航栏添加现代化的动画和过渡效果
 */

/* 导航栏整体动画 */
.navbar {
  transition: all 0.3s ease-in-out;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 导航链接动画效果 */
.navbar .nav-link {
  position: relative;
  transition: all 0.3s ease;
  padding: 0.5rem 1rem;
  margin: 0 0.2rem;
  border-radius: 4px;
}

/* 导航链接悬停效果 */
.navbar .nav-link:hover {
  color: #ffffff !important;
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

/* 导航链接激活状态 */
.navbar .nav-link.active {
  color: #ffffff !important;
  background-color: rgba(255, 255, 255, 0.15);
  font-weight: 500;
}

/* 导航链接下划线动画 */
.navbar .nav-link::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 50%;
  background-color: #ffffff;
  transition: all 0.3s ease;
  transform: translateX(-50%);
  opacity: 0;
}

.navbar .nav-link:hover::after,
.navbar .nav-link.active::after {
  width: 80%;
  opacity: 1;
}

/* 下拉菜单动画 */
.navbar .dropdown-menu {
  display: block;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
  border: none;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  padding: 0.5rem;
}

.navbar .dropdown:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

/* 下拉菜单项动画 */
.navbar .dropdown-item {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.navbar .dropdown-item:hover {
  background-color: rgba(29, 161, 242, 0.1);
  color: var(--primary-color);
  transform: translateX(5px);
}

.navbar .dropdown-item i {
  margin-right: 0.5rem;
  transition: all 0.2s ease;
}

.navbar .dropdown-item:hover i {
  transform: scale(1.2);
}

/* 导航栏品牌名称动画 */
.navbar-brand {
  position: relative;
  transition: all 0.3s ease;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.navbar-brand:hover {
  transform: scale(1.05);
}

/* 导航栏切换按钮动画 */
.navbar-toggler {
  border: none;
  background: transparent;
  transition: all 0.3s ease;
}

.navbar-toggler:hover {
  transform: rotate(90deg);
}

.navbar-toggler:focus {
  box-shadow: none;
}

/* 通知图标动画 */
.navbar .nav-link .bi-bell {
  transition: all 0.3s ease;
}

.navbar .nav-link:hover .bi-bell {
  transform: rotate(15deg) scale(1.2);
}

/* 通知徽章动画 */
.navbar .badge {
  transition: all 0.3s ease;
}

.navbar .nav-link:hover .badge {
  transform: scale(1.2);
}

/* 响应式调整 */
@media (max-width: 992px) {
  .navbar .nav-link::after {
    display: none;
  }
  
  .navbar .nav-link:hover {
    transform: none;
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  .navbar .dropdown-menu {
    display: none;
    opacity: 1;
    visibility: visible;
    transform: none;
    box-shadow: none;
    padding: 0;
    margin: 0;
    border-radius: 0;
    background-color: rgba(0, 0, 0, 0.1);
  }
  
  .navbar .dropdown-menu.show {
    display: block;
  }
  
  .navbar .dropdown-item:hover {
    transform: none;
  }
}
