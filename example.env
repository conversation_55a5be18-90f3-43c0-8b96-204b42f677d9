REDIS_URL=

# openai endpoint settings
LLM_API_MODEL=
LLM_API_KEY=
LLM_API_BASE=
LLM_PROCESS_MAX_RETRIED=3

## wecom bots
ENABLE_WECOM_BOT=
DEBUG_ROBOT_ID=
WECOM_TRUMP_ROBOT_ID=
WECOM_FINANCE_ROBOT_ID=
WECOM_AI_ROBOT_ID=

## wechat bot based on Gewechat
ENABLE_WECHAT_BOT=
WECHAT_ROBOT_IP=
WECHAT_ROBOT_TOKEN=
WECHAT_ROBOT_APP_ID=
WECHAT_ROBOT_CHATROOM_ID=

## qq bot based on lagrange
ENABLE_QQ_BOT=
QQ_BOT_URL=
QQ_BOT_GROUP_ID=

# Twitter设置
TWITTER_USERNAME=
TWITTER_PASSWORD=
TWITTER_SESSION=
TRUTHSOCIAL_TOKEN=

# 代理设置
# 支持HTTP和SOCKS代理，例如:
# HTTP_PROXY=http://proxy.example.com:8080
# 或SOCKS代理:
# HTTP_PROXY=socks5://proxy.example.com:1080
HTTP_PROXY=
HTTPS_PROXY=