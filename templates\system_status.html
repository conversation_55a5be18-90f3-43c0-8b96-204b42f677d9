{% extends "base.html" %}

{% block title %}系统状态 - TweetAnalyst{% endblock %}

{% block extra_css %}
<style>
    .status-card {
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .status-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    }
    
    .status-badge {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
    }
    
    .status-normal {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .status-warning {
        background-color: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }
    
    .status-error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    .status-disabled {
        background-color: #e2e3e5;
        color: #6c757d;
        border: 1px solid #d6d8db;
    }
    
    .detail-item {
        padding: 0.5rem 0;
        border-bottom: 1px solid #f8f9fa;
    }
    
    .detail-item:last-child {
        border-bottom: none;
    }
    
    .refresh-btn {
        transition: transform 0.3s ease;
    }
    
    .refresh-btn:hover {
        transform: rotate(180deg);
    }
    
    .component-icon {
        font-size: 2rem;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 页面标题和面包屑导航 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ url_for('index') }}">首页</a></li>
                    <li class="breadcrumb-item active" aria-current="page">系统状态</li>
                </ol>
            </nav>
            <h2 class="mt-2">
                <i class="bi bi-activity text-primary me-2"></i>系统状态监控
            </h2>
        </div>
        <div>
            <button class="btn btn-outline-primary refresh-btn" onclick="refreshStatus()">
                <i class="bi bi-arrow-clockwise me-1"></i>刷新状态
            </button>
            <a href="{{ url_for('logs_page') }}" class="btn btn-outline-secondary ms-2">
                <i class="bi bi-journal-text me-1"></i>系统日志
            </a>
        </div>
    </div>

    <!-- 系统状态概览 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card status-card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-speedometer2 me-2"></i>系统状态概览
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-2">
                            <div class="component-icon text-primary">
                                <i class="bi bi-hdd-stack"></i>
                            </div>
                            <h6>数据库</h6>
                            <span class="status-badge" id="db-status-badge">检查中...</span>
                        </div>
                        <div class="col-md-2">
                            <div class="component-icon text-success">
                                <i class="bi bi-robot"></i>
                            </div>
                            <h6>AI服务</h6>
                            <span class="status-badge" id="ai-status-badge">检查中...</span>
                        </div>
                        <div class="col-md-2">
                            <div class="component-icon text-info">
                                <i class="bi bi-bell"></i>
                            </div>
                            <h6>推送服务</h6>
                            <span class="status-badge" id="notification-status-badge">检查中...</span>
                        </div>
                        <div class="col-md-2">
                            <div class="component-icon text-warning">
                                <i class="bi bi-shield-check"></i>
                            </div>
                            <h6>代理服务</h6>
                            <span class="status-badge" id="proxy-status-badge">检查中...</span>
                        </div>
                        <div class="col-md-2">
                            <div class="component-icon text-primary">
                                <i class="bi bi-twitter"></i>
                            </div>
                            <h6>Twitter</h6>
                            <span class="status-badge" id="twitter-status-badge">检查中...</span>
                        </div>
                        <div class="col-md-2">
                            <div class="component-icon text-danger">
                                <i class="bi bi-gear-wide-connected"></i>
                            </div>
                            <h6>核心抓取</h6>
                            <span class="status-badge" id="core-status-badge">检查中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详细状态信息 -->
    <div class="row">
        <!-- 数据库状态 -->
        <div class="col-lg-6 mb-4">
            <div class="card status-card h-100">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-hdd-stack text-primary me-2"></i>数据库状态
                    </h6>
                </div>
                <div class="card-body" id="database-details">
                    <div class="text-center text-muted">
                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                        正在检查数据库状态...
                    </div>
                </div>
            </div>
        </div>

        <!-- AI服务状态 -->
        <div class="col-lg-6 mb-4">
            <div class="card status-card h-100">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-robot text-success me-2"></i>AI服务状态
                    </h6>
                </div>
                <div class="card-body" id="ai-details">
                    <div class="text-center text-muted">
                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                        正在检查AI服务状态...
                    </div>
                </div>
            </div>
        </div>

        <!-- 推送服务状态 -->
        <div class="col-lg-6 mb-4">
            <div class="card status-card h-100">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-bell text-info me-2"></i>推送服务状态
                    </h6>
                </div>
                <div class="card-body" id="notification-details">
                    <div class="text-center text-muted">
                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                        正在检查推送服务状态...
                    </div>
                </div>
            </div>
        </div>

        <!-- 代理服务状态 -->
        <div class="col-lg-6 mb-4">
            <div class="card status-card h-100">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-shield-check text-warning me-2"></i>代理服务状态
                    </h6>
                </div>
                <div class="card-body" id="proxy-details">
                    <div class="text-center text-muted">
                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                        正在检查代理服务状态...
                    </div>
                </div>
            </div>
        </div>

        <!-- Twitter服务状态 -->
        <div class="col-lg-6 mb-4">
            <div class="card status-card h-100">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-twitter text-primary me-2"></i>Twitter服务状态
                    </h6>
                </div>
                <div class="card-body" id="twitter-details">
                    <div class="text-center text-muted">
                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                        正在检查Twitter服务状态...
                    </div>
                </div>
            </div>
        </div>

        <!-- 核心抓取组件状态 -->
        <div class="col-lg-6 mb-4">
            <div class="card status-card h-100">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-gear-wide-connected text-danger me-2"></i>核心抓取组件状态
                    </h6>
                </div>
                <div class="card-body" id="core-details">
                    <div class="text-center text-muted">
                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                        正在检查核心抓取组件状态...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 最后更新时间 -->
    <div class="row">
        <div class="col-12">
            <div class="text-center text-muted">
                <small>
                    <i class="bi bi-clock me-1"></i>
                    最后更新时间: <span id="last-update-time">-</span>
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 页面加载时检查系统状态
    document.addEventListener('DOMContentLoaded', function() {
        checkSystemStatus();
    });

    // 刷新状态
    function refreshStatus() {
        checkSystemStatus();
    }

    // 检查系统状态
    function checkSystemStatus() {
        // 显示加载状态
        showLoadingState();
        
        fetch('/api/system/status')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateSystemStatus(data);
                } else {
                    showErrorState(data.message || '获取系统状态失败');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showErrorState('网络错误或服务不可用');
            });
    }

    // 显示加载状态
    function showLoadingState() {
        const components = ['db', 'ai', 'notification', 'proxy', 'twitter', 'core'];
        components.forEach(component => {
            const badge = document.getElementById(`${component}-status-badge`);
            if (badge) {
                badge.textContent = '检查中...';
                badge.className = 'status-badge status-warning';
            }
        });
    }

    // 更新系统状态
    function updateSystemStatus(data) {
        // 更新状态徽章
        updateStatusBadge('db-status-badge', data.database_status);
        updateStatusBadge('ai-status-badge', data.ai_status);
        updateStatusBadge('notification-status-badge', data.notification_status);
        updateStatusBadge('proxy-status-badge', data.proxy_status);
        updateStatusBadge('twitter-status-badge', data.twitter_status);
        updateStatusBadge('core-status-badge', data.core_scraping_status);

        // 更新详细信息
        updateDetailPanel('database-details', data.database_status, data.database_message, data.database_details);
        updateDetailPanel('ai-details', data.ai_status, data.ai_message, data.ai_details);
        updateDetailPanel('notification-details', data.notification_status, data.notification_message, data.notification_details);
        updateDetailPanel('proxy-details', data.proxy_status, data.proxy_message, data.proxy_details);
        updateDetailPanel('twitter-details', data.twitter_status, data.twitter_message, data.twitter_details);
        updateDetailPanel('core-details', data.core_scraping_status, data.core_scraping_message, data.core_scraping_details);

        // 更新最后更新时间
        document.getElementById('last-update-time').textContent = new Date().toLocaleString();
    }

    // 更新状态徽章
    function updateStatusBadge(elementId, status) {
        const badge = document.getElementById(elementId);
        if (!badge) return;

        badge.className = `status-badge status-${status}`;
        
        switch(status) {
            case 'normal':
                badge.innerHTML = '<i class="bi bi-check-circle me-1"></i>正常';
                break;
            case 'warning':
                badge.innerHTML = '<i class="bi bi-exclamation-triangle me-1"></i>警告';
                break;
            case 'error':
                badge.innerHTML = '<i class="bi bi-x-circle me-1"></i>错误';
                break;
            case 'disabled':
                badge.innerHTML = '<i class="bi bi-dash-circle me-1"></i>已禁用';
                break;
            default:
                badge.innerHTML = '<i class="bi bi-question-circle me-1"></i>未知';
        }
    }

    // 更新详细信息面板
    function updateDetailPanel(elementId, status, message, details) {
        const panel = document.getElementById(elementId);
        if (!panel) return;

        let html = `
            <div class="mb-3">
                <span class="status-badge status-${status}">
                    ${getStatusIcon(status)} ${getStatusText(status)}
                </span>
            </div>
            <p class="mb-3">${message}</p>
        `;

        if (details && Object.keys(details).length > 0) {
            html += '<h6 class="text-muted mb-2">详细信息:</h6>';
            for (const [key, value] of Object.entries(details)) {
                html += `
                    <div class="detail-item">
                        <strong>${formatKey(key)}:</strong> 
                        <span class="text-muted">${formatValue(value)}</span>
                    </div>
                `;
            }
        }

        panel.innerHTML = html;
    }

    // 获取状态图标
    function getStatusIcon(status) {
        switch(status) {
            case 'normal': return '<i class="bi bi-check-circle me-1"></i>';
            case 'warning': return '<i class="bi bi-exclamation-triangle me-1"></i>';
            case 'error': return '<i class="bi bi-x-circle me-1"></i>';
            case 'disabled': return '<i class="bi bi-dash-circle me-1"></i>';
            default: return '<i class="bi bi-question-circle me-1"></i>';
        }
    }

    // 获取状态文本
    function getStatusText(status) {
        switch(status) {
            case 'normal': return '正常';
            case 'warning': return '警告';
            case 'error': return '错误';
            case 'disabled': return '已禁用';
            default: return '未知';
        }
    }

    // 格式化键名
    function formatKey(key) {
        const keyMap = {
            'result_count': '分析结果数量',
            'account_count': '监控账号数量',
            'db_size_mb': '数据库大小(MB)',
            'db_path': '数据库路径',
            'api_key_configured': 'API密钥配置',
            'api_base': 'API基础URL',
            'model': '模型',
            'module_loaded': '模块加载状态',
            'enabled': '启用状态',
            'url_configured': 'URL配置',
            'push_url': '推送URL',
            'host': '主机',
            'port': '端口',
            'username_configured': '用户名配置',
            'password_configured': '密码配置',
            'library_preference': '库偏好',
            'tweety_available': 'Tweety库可用',
            'twikit_available': 'Twikit库可用',
            'main': '主模块',
            'post': 'Post模块',
            'twitter_utils': 'Twitter工具模块'
        };
        return keyMap[key] || key;
    }

    // 格式化值
    function formatValue(value) {
        if (typeof value === 'boolean') {
            return value ? '✅ 是' : '❌ 否';
        }
        return value;
    }

    // 显示错误状态
    function showErrorState(message) {
        const components = ['db', 'ai', 'notification', 'proxy', 'twitter', 'core'];
        components.forEach(component => {
            const badge = document.getElementById(`${component}-status-badge`);
            if (badge) {
                badge.innerHTML = '<i class="bi bi-x-circle me-1"></i>错误';
                badge.className = 'status-badge status-error';
            }
            
            const details = document.getElementById(`${component}-details`);
            if (details) {
                details.innerHTML = `
                    <div class="text-center text-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        ${message}
                    </div>
                `;
            }
        });
        
        document.getElementById('last-update-time').textContent = new Date().toLocaleString();
    }
</script>
{% endblock %}
