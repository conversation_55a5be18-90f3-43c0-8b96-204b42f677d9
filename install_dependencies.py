#!/usr/bin/env python3
"""
依赖安装脚本
自动检测和安装项目依赖
"""

import os
import sys
import subprocess
import importlib
import logging
from pathlib import Path

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('install_dependencies.log')
        ]
    )

def check_python_version():
    """检查Python版本"""
    logger = logging.getLogger(__name__)
    
    if sys.version_info < (3, 8):
        logger.error(f"Python版本过低: {sys.version}")
        logger.error("需要Python 3.8或更高版本")
        return False
    
    logger.info(f"Python版本: {sys.version}")
    return True

def check_pip():
    """检查pip是否可用"""
    logger = logging.getLogger(__name__)
    
    try:
        import pip
        logger.info("pip已安装")
        return True
    except ImportError:
        logger.error("pip未安装")
        return False

def install_package(package_name):
    """安装单个包"""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info(f"安装 {package_name}...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", package_name
        ])
        logger.info(f"{package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"{package_name} 安装失败: {e}")
        return False

def check_package(package_name, import_name=None):
    """检查包是否已安装"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        return True
    except ImportError:
        return False

def install_from_requirements():
    """从requirements.txt安装依赖"""
    logger = logging.getLogger(__name__)
    
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        logger.warning("未找到requirements.txt文件")
        return False
    
    try:
        logger.info("从requirements.txt安装依赖...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ])
        logger.info("requirements.txt安装完成")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"requirements.txt安装失败: {e}")
        return False

def install_essential_packages():
    """安装必要的包"""
    logger = logging.getLogger(__name__)
    
    essential_packages = [
        ("flask", "flask"),
        ("requests", "requests"),
        ("schedule", "schedule"),
        ("python-dotenv", "dotenv"),
        ("pyyaml", "yaml"),
        ("flask-sqlalchemy", "flask_sqlalchemy"),
        ("flask-wtf", "flask_wtf"),
        ("flask-login", "flask_login"),
        ("apprise", "apprise"),
        ("tweety-ns", "tweety"),
        ("langchain-openai", "langchain_openai"),
        ("langchain-core", "langchain_core"),
    ]
    
    failed_packages = []
    
    for package_name, import_name in essential_packages:
        if not check_package(package_name, import_name):
            logger.info(f"{package_name} 未安装，正在安装...")
            if not install_package(package_name):
                failed_packages.append(package_name)
        else:
            logger.info(f"{package_name} 已安装")
    
    if failed_packages:
        logger.error(f"以下包安装失败: {failed_packages}")
        return False
    
    logger.info("所有必要包安装完成")
    return True

def upgrade_pip():
    """升级pip"""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("升级pip...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "--upgrade", "pip"
        ])
        logger.info("pip升级完成")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"pip升级失败: {e}")
        return False

def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("开始安装依赖...")
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查pip
    if not check_pip():
        logger.error("请先安装pip")
        sys.exit(1)
    
    # 升级pip
    upgrade_pip()
    
    # 优先从requirements.txt安装
    if install_from_requirements():
        logger.info("依赖安装完成")
    else:
        # 如果requirements.txt安装失败，尝试安装必要包
        logger.info("尝试安装必要包...")
        if not install_essential_packages():
            logger.error("依赖安装失败")
            sys.exit(1)
    
    logger.info("所有依赖安装完成！")

if __name__ == "__main__":
    main()
-------原始代码----

#!/usr/bin/env python
"""
依赖安装脚本

此脚本用于安装项目所需的依赖，包括基本依赖和可选依赖。
"""
import os
import sys
import subprocess
import argparse
import importlib.util
from typing import List, Dict, Tuple, Optional

def install_package(package):
    """
    安装指定的包

    Args:
        package (str): 包名
    """
    print(f"正在安装 {package}...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ {package} 安装成功")
        return True
    except subprocess.CalledProcessError:
        print(f"✗ {package} 安装失败")
        return False

def install_from_requirements(requirements_file, optional=False):
    """
    从requirements.txt文件安装依赖

    Args:
        requirements_file (str): requirements.txt文件路径
        optional (bool): 是否安装可选依赖
    """
    if not os.path.exists(requirements_file):
        print(f"错误: 找不到文件 {requirements_file}")
        return False

    print(f"正在从 {requirements_file} 安装{'可选' if optional else '基本'}依赖...")

    with open(requirements_file, 'r') as f:
        lines = f.readlines()

    installed_count = 0
    failed_count = 0

    for line in lines:
        line = line.strip()

        # 跳过空行和注释
        if not line or line.startswith('#'):
            continue

        # 处理可选依赖
        if optional and not line.startswith('#'):
            continue

        if optional and line.startswith('# '):
            # 去掉注释符号和说明文字
            package = line[2:].split('#')[0].strip()
        else:
            package = line

        if install_package(package):
            installed_count += 1
        else:
            failed_count += 1

    print(f"\n安装完成: {installed_count} 个包安装成功, {failed_count} 个包安装失败")
    return failed_count == 0

def check_package(module_name: str) -> bool:
    """
    检查包是否已安装

    Args:
        module_name: 模块名称

    Returns:
        bool: 是否已安装
    """
    return importlib.util.find_spec(module_name) is not None

def check_proxy_support() -> bool:
    """
    检查代理支持

    Returns:
        bool: 是否支持SOCKS代理
    """
    # 尝试使用代理管理器
    try:
        # 动态导入代理管理器，避免循环导入
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        try:
            from utils.api_utils import get_proxy_manager

            # 获取代理管理器
            proxy_manager = get_proxy_manager()

            # 查找可用代理
            working_proxy = proxy_manager.find_working_proxy()

            if working_proxy and working_proxy.protocol.startswith('socks'):
                print(f"检测到SOCKS代理: {working_proxy.name}")
                if check_package('socksio'):
                    print("✓ SOCKS代理支持已安装")
                    return True
                else:
                    print("! 未安装SOCKS代理支持，尝试安装...")
                    return install_package('httpx[socks]')
        except ImportError:
            print("未找到代理管理器，使用传统方式检查代理")
    except Exception as e:
        print(f"使用代理管理器检查代理时出错: {str(e)}")

    # 回退到传统方式
    proxy = os.getenv('HTTP_PROXY', '')
    if proxy and proxy.startswith('socks'):
        print(f"检测到SOCKS代理: {proxy}")
        if check_package('socksio'):
            print("✓ SOCKS代理支持已安装")
            return True
        else:
            print("! 未安装SOCKS代理支持，尝试安装...")
            return install_package('httpx[socks]')
    return True

def main():
    parser = argparse.ArgumentParser(description='安装项目依赖')
    parser.add_argument('--all', action='store_true', help='安装所有依赖，包括可选依赖')
    parser.add_argument('--optional', action='store_true', help='只安装可选依赖')
    parser.add_argument('--proxy', action='store_true', help='检查并安装代理支持')
    args = parser.parse_args()

    # 检查代理支持
    if args.proxy or os.getenv('HTTP_PROXY', '').startswith('socks'):
        check_proxy_support()

    requirements_file = 'requirements.txt'

    if args.optional:
        # 只安装可选依赖
        install_from_requirements(requirements_file, optional=True)
    elif args.all:
        # 安装所有依赖
        install_from_requirements(requirements_file, optional=False)
        # 取消注释可选依赖并安装
        with open(requirements_file, 'r') as f:
            content = f.read()

        temp_file = 'temp_requirements.txt'
        with open(temp_file, 'w') as f:
            # 去掉可选依赖前的注释符号
            modified_content = content.replace('# psutil', 'psutil')
            modified_content = modified_content.replace('# redis', 'redis')
            modified_content = modified_content.replace('# beautifulsoup4', 'beautifulsoup4')
            modified_content = modified_content.replace('# gunicorn', 'gunicorn')
            modified_content = modified_content.replace('# httpx[socks]', 'httpx[socks]')
            f.write(modified_content)

        install_from_requirements(temp_file, optional=False)

        # 删除临时文件
        if os.path.exists(temp_file):
            os.remove(temp_file)
    else:
        # 安装基本依赖
        install_from_requirements(requirements_file, optional=False)

    print("\n依赖安装完成！")

    # 检查是否使用SOCKS代理
    proxy = os.getenv('HTTP_PROXY', '')
    if proxy and proxy.startswith('socks'):
        if not check_package('socksio'):
            print("\n警告: 您正在使用SOCKS代理，但未安装SOCKS代理支持。")
            print("请运行以下命令安装SOCKS代理支持:")
            print("    pip install httpx[socks]")
            print("或者使用此脚本的--proxy参数:")
            print("    python install_dependencies.py --proxy")

    print("\n您可以通过以下命令运行应用:")
    print("1. 运行Web应用: python run_web.py")
    print("2. 运行定时任务: python run_scheduler.py")
    print("3. 同时运行Web应用和定时任务: python run_all.py")

if __name__ == "__main__":
    main()
