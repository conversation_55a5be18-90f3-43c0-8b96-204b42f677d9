#!/usr/bin/env python3
"""
依赖安装脚本
自动检测和安装项目依赖
"""

import os
import sys
import subprocess
import importlib
import logging
from pathlib import Path

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('install_dependencies.log')
        ]
    )

def check_python_version():
    """检查Python版本"""
    logger = logging.getLogger(__name__)
    
    if sys.version_info < (3, 8):
        logger.error(f"Python版本过低: {sys.version}")
        logger.error("需要Python 3.8或更高版本")
        return False
    
    logger.info(f"Python版本: {sys.version}")
    return True

def check_pip():
    """检查pip是否可用"""
    logger = logging.getLogger(__name__)
    
    try:
        import pip
        logger.info("pip已安装")
        return True
    except ImportError:
        logger.error("pip未安装")
        return False

def install_package(package_name):
    """安装单个包"""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info(f"安装 {package_name}...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", package_name
        ])
        logger.info(f"{package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"{package_name} 安装失败: {e}")
        return False

def check_package(package_name, import_name=None):
    """检查包是否已安装"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        return True
    except ImportError:
        return False

def install_from_requirements():
    """从requirements.txt安装依赖"""
    logger = logging.getLogger(__name__)
    
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        logger.warning("未找到requirements.txt文件")
        return False
    
    try:
        logger.info("从requirements.txt安装依赖...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ])
        logger.info("requirements.txt安装完成")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"requirements.txt安装失败: {e}")
        return False

def install_essential_packages():
    """安装必要的包"""
    logger = logging.getLogger(__name__)
    
    essential_packages = [
        ("flask", "flask"),
        ("requests", "requests"),
        ("schedule", "schedule"),
        ("python-dotenv", "dotenv"),
        ("pyyaml", "yaml"),
        ("flask-sqlalchemy", "flask_sqlalchemy"),
        ("flask-wtf", "flask_wtf"),
        ("flask-login", "flask_login"),
        ("apprise", "apprise"),
        ("tweety-ns", "tweety"),
        ("langchain-openai", "langchain_openai"),
        ("langchain-core", "langchain_core"),
    ]
    
    failed_packages = []
    
    for package_name, import_name in essential_packages:
        if not check_package(package_name, import_name):
            logger.info(f"{package_name} 未安装，正在安装...")
            if not install_package(package_name):
                failed_packages.append(package_name)
        else:
            logger.info(f"{package_name} 已安装")
    
    if failed_packages:
        logger.error(f"以下包安装失败: {failed_packages}")
        return False
    
    logger.info("所有必要包安装完成")
    return True

def upgrade_pip():
    """升级pip"""
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("升级pip...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "--upgrade", "pip"
        ])
        logger.info("pip升级完成")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"pip升级失败: {e}")
        return False

def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("开始安装依赖...")
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查pip
    if not check_pip():
        logger.error("请先安装pip")
        sys.exit(1)
    
    # 升级pip
    upgrade_pip()
    
    # 优先从requirements.txt安装
    if install_from_requirements():
        logger.info("依赖安装完成")
    else:
        # 如果requirements.txt安装失败，尝试安装必要包
        logger.info("尝试安装必要包...")
        if not install_essential_packages():
            logger.error("依赖安装失败")
            sys.exit(1)
    
    logger.info("所有依赖安装完成！")

if __name__ == "__main__":
    main()
