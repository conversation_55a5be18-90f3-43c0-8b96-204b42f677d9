#!/usr/bin/env python3
"""
推送队列处理器启动脚本
单独运行推送队列处理服务
"""

import os
import sys
import logging
import signal
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from services.push_queue_worker import PushQueueWorker
from utils.logger import setup_logger

def setup_logging():
    """设置日志"""
    logger = setup_logger('push_queue', 'push_queue.log')
    return logger

def signal_handler(signum, frame):
    """信号处理器"""
    logger = logging.getLogger('push_queue')
    logger.info(f"接收到信号 {signum}，正在关闭推送队列处理器...")
    sys.exit(0)

def main():
    """主函数"""
    logger = setup_logging()
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    logger.info("启动推送队列处理器...")
    
    try:
        # 创建推送队列工作器
        worker = PushQueueWorker()
        
        # 启动工作器
        worker.start()
        
        logger.info("推送队列处理器已启动，按 Ctrl+C 停止")
        
        # 保持运行
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("接收到中断信号，正在停止...")
    except Exception as e:
        logger.error(f"推送队列处理器运行失败: {e}")
        sys.exit(1)
    finally:
        if 'worker' in locals():
            worker.stop()
        logger.info("推送队列处理器已停止")

if __name__ == "__main__":
    main()
