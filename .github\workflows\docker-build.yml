name: Docker 镜像构建与发布

on:
  workflow_dispatch:  # 只允许手动触发
    inputs:
      version:
        description: '版本号 (例如: 1.0.0)'
        required: true
        default: 'latest'
      description:
        description: '构建描述'
        required: false
        default: '手动触发的构建'

jobs:
  # 为每个平台单独构建镜像
  build-per-platform:
    name: 构建 ${{ matrix.platform }} 平台镜像
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false  # 即使一个平台失败，也继续构建其他平台
      max-parallel: 2   # 限制并行运行的作业数量，只有两个架构
      matrix:
        include:
          - platform: linux/amd64
            timeout: 30
          - platform: linux/arm64
            timeout: 60

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 QEMU
        uses: docker/setup-qemu-action@v2
        with:
          platforms: ${{ matrix.platform }}

      - name: 设置 Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 登录 DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_TOKEN }}

      # 提取平台名称作为标签后缀
      - name: 提取平台后缀
        id: platform_suffix
        run: |
          SUFFIX=$(echo "${{ matrix.platform }}" | sed 's/linux\///' | sed 's/\//-/')
          echo "suffix=$SUFFIX" >> $GITHUB_OUTPUT

      # 使用手动输入的版本信息
      - name: 提取版本信息
        id: version_info
        run: |
          VERSION="${{ github.event.inputs.version }}"
          if [[ "$VERSION" == "latest" ]]; then
            echo "version=latest" >> $GITHUB_OUTPUT
            echo "tag_suffix=" >> $GITHUB_OUTPUT
          else
            echo "version=$VERSION" >> $GITHUB_OUTPUT
            echo "tag_suffix=-$VERSION" >> $GITHUB_OUTPUT
          fi
          echo "使用版本: $VERSION"

      # 构建并推送单平台镜像
      # 设置构建缓存
      - name: 设置 Docker Buildx 缓存
        uses: actions/cache@v3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ matrix.platform }}-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-${{ matrix.platform }}-
            ${{ runner.os }}-buildx-

      # 列出工作目录内容（调试用）
      - name: 列出工作目录内容
        run: |
          echo "当前工作目录: $(pwd)"
          ls -la

      # 创建 requirements.txt 文件
      - name: 创建 requirements.txt 文件
        run: |
          echo "创建 requirements.txt 文件..."
          cat > requirements.txt << 'EOL'
          # 基础依赖
          python-dotenv>=1.0.0
          pyyaml>=6.0
          requests>=2.28.2
          pytz>=2023.3
          schedule>=1.2.0

          # LLM支持
          langchain-openai>=0.0.1
          langchain-core>=0.1.0

          # Twitter支持
          tweety-ns>=2.2
          twikit>=2.1.0

          # Web应用
          Flask>=2.0.0,<2.4.0
          Flask-SQLAlchemy>=3.0.0
          Flask-WTF>=1.0.0,<1.2.0
          Flask-Login>=0.6.0
          Werkzeug>=2.0.0,<2.4.0

          # 系统监控
          psutil>=5.9.0

          # 推送通知
          apprise>=1.9.0

          # SOCKS代理支持
          httpx>=0.24.0
          EOL

          echo "requirements.txt 文件已创建:"
          cat requirements.txt

      # 创建docker-entrypoint.sh脚本
      - name: 创建docker-entrypoint.sh脚本
        run: |
          echo "创建docker-entrypoint.sh脚本..."
          cat > docker-entrypoint.sh << 'EOL'
          #!/bin/bash
          set -e

          # 设置颜色
          GREEN='\033[0;32m'
          YELLOW='\033[0;33m'
          RED='\033[0;31m'
          NC='\033[0m' # No Color

          echo -e "${GREEN}===== TweetAnalyst应用启动脚本 =====${NC}"

          # 创建必要的目录
          echo -e "${GREEN}创建必要的目录...${NC}"
          mkdir -p /data /app/logs
          chmod -R 755 /data /app/logs

          # 检查数据库目录权限
          if [ ! -w "/data" ]; then
              echo -e "${RED}警告: /data 目录没有写入权限，可能导致数据库无法创建${NC}"
              echo -e "${YELLOW}尝试修复权限...${NC}"
              chmod -R 755 /data
              if [ ! -w "/data" ]; then
                  echo -e "${RED}无法修复 /data 目录权限，请检查卷挂载设置${NC}"
                  # 在CI/CD环境中，这是一个严重错误，应该立即退出
                  exit 1
              else
                  echo -e "${GREEN}已修复 /data 目录权限${NC}"
              fi
          fi

          # 检查代理设置
          if [[ -n "$HTTP_PROXY" ]]; then
              echo -e "${GREEN}检测到HTTP_PROXY环境变量: $HTTP_PROXY${NC}"

              # 特殊处理SOCKS代理支持
              if [[ "$HTTP_PROXY" == socks* ]]; then
                  echo -e "${YELLOW}检测到SOCKS代理${NC}"
              fi
          fi

          if [[ -n "$HTTPS_PROXY" ]]; then
              echo -e "${GREEN}检测到HTTPS_PROXY环境变量: $HTTPS_PROXY${NC}"

              # 特殊处理SOCKS代理支持
              if [[ "$HTTPS_PROXY" == socks* ]]; then
                  echo -e "${YELLOW}检测到SOCKS代理${NC}"
              fi
          fi

          # 依赖已在Dockerfile中安装
          echo -e "${GREEN}所有依赖已在镜像构建时安装${NC}"

          # 设置默认LLM API
          if [ -z "$LLM_API_BASE" ]; then
              echo -e "${YELLOW}未设置LLM_API_BASE，使用默认值: https://api.x.ai/v1${NC}"
              export LLM_API_BASE="https://api.x.ai/v1"
          fi

          if [ -z "$LLM_API_MODEL" ]; then
              echo -e "${YELLOW}未设置LLM_API_MODEL，使用默认值: grok-3-mini-beta${NC}"
              export LLM_API_MODEL="grok-3-mini-beta"
          fi

          # 确保使用正确的API端点
          echo -e "${GREEN}使用API基础URL: $LLM_API_BASE${NC}"
          echo -e "${GREEN}使用模型: $LLM_API_MODEL${NC}"

          # 检查数据库文件
          if [ ! -f "/data/tweetAnalyst.db" ]; then
              echo -e "${YELLOW}数据库文件不存在，将在首次运行时创建${NC}"

              # 如果FIRST_LOGIN设置为true，记录信息
              if [ "$FIRST_LOGIN" = "true" ]; then
                  echo -e "${YELLOW}FIRST_LOGIN设置为true，将强制初始化数据库${NC}"
              fi
          else
              echo -e "${GREEN}检测到现有数据库文件${NC}"

              # 如果FIRST_LOGIN设置为true，记录警告
              if [ "$FIRST_LOGIN" = "true" ]; then
                  echo -e "${RED}警告: FIRST_LOGIN设置为true，但数据库文件已存在${NC}"
                  echo -e "${RED}这可能导致数据丢失，请确认是否需要重置数据库${NC}"
              fi
          fi

          # 启动应用
          echo -e "${GREEN}启动应用...${NC}"
          exec python run_all.py
          EOL
          chmod +x docker-entrypoint.sh
          echo "docker-entrypoint.sh脚本已创建"

      # 创建临时构建目录
      - name: 创建临时构建目录
        run: |
          mkdir -p /tmp/build
          cp -r . /tmp/build/
          ls -la /tmp/build/

          # 创建优化版的Dockerfile
          echo "创建优化版的Dockerfile..."
          cat > /tmp/build/Dockerfile << 'EOL'
          FROM python:3.11-slim

          LABEL maintainer="TweetAnalyst Team & Augment Code"
          LABEL description="TweetAnalyst - 社交媒体内容分析助手"
          LABEL version="2.0"
          LABEL website="https://augmentcode.com"
          LABEL credits="本项目由一个写不出一行代码的白痴在Augment Code的强大帮助下完成"

          WORKDIR /app

          # 安装系统依赖
          RUN apt-get update && apt-get install -y --no-install-recommends \
              gcc \
              python3-dev \
              build-essential \
              curl \
              && rm -rf /var/lib/apt/lists/*

          # 升级pip和基本工具
          RUN pip install --no-cache-dir --upgrade pip setuptools wheel

          # 复制requirements.txt
          COPY requirements.txt /app/requirements.txt

          # 安装所有依赖（合并多个RUN命令减少层数）
          RUN pip install --no-cache-dir -r /app/requirements.txt && \
              pip install --no-cache-dir langchain-openai>=0.0.1 langchain-core>=0.1.0 && \
              pip install --no-cache-dir Flask Flask-WTF Werkzeug Flask-SQLAlchemy Flask-Login>=0.6.0 && \
              pip install --no-cache-dir tweety-ns>=2.2 twikit>=2.1.0 apprise "httpx[socks]" && \
              pip cache purge

          # 创建数据和日志目录
          RUN mkdir -p /data /app/logs && \
              chmod -R 755 /app/logs && \
              chmod -R 755 /data

          # 先复制启动脚本并设置权限
          COPY docker-entrypoint.sh /app/docker-entrypoint.sh
          RUN chmod +x /app/docker-entrypoint.sh

          # 复制其余应用代码
          COPY . .

          # 设置环境变量
          ENV DATABASE_PATH=/data/tweetAnalyst.db \
              FLASK_SECRET_KEY=default_secret_key_please_change_in_env \
              LOG_DIR=/app/logs \
              FIRST_LOGIN=auto \
              FLASK_DEBUG=false \
              LLM_API_BASE=https://api.x.ai/v1 \
              LLM_API_MODEL=grok-3-mini-beta \
              PYTHONUNBUFFERED=1 \
              TZ=Asia/Shanghai

          # 暴露端口
          EXPOSE 5000

          # 健康检查
          HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
              CMD curl -f http://localhost:5000/ || exit 1

          # 启动命令
          CMD ["/app/docker-entrypoint.sh"]
          EOL
          echo "简化版的Dockerfile已创建"

          # 检查临时目录中的 requirements.txt 文件
          if [ -f "/tmp/build/requirements.txt" ]; then
            echo "临时目录中 requirements.txt 存在"
            cat /tmp/build/requirements.txt | head -5
          else
            echo "临时目录中 requirements.txt 不存在！"
            # 复制刚刚创建的 requirements.txt 文件
            cp requirements.txt /tmp/build/
            echo "已复制 requirements.txt 到临时目录"
            cat /tmp/build/requirements.txt | head -5
          fi

          # 确保docker-entrypoint.sh脚本存在并有执行权限
          if [ -f "docker-entrypoint.sh" ]; then
            echo "源目录中 docker-entrypoint.sh 存在"
            cp docker-entrypoint.sh /tmp/build/
            chmod +x /tmp/build/docker-entrypoint.sh
            echo "已复制 docker-entrypoint.sh 到临时目录并设置执行权限"
          else
            echo "❌ 源目录中 docker-entrypoint.sh 不存在！"
            exit 1
          fi

          # 验证文件是否正确复制
          if [ -f "/tmp/build/docker-entrypoint.sh" ]; then
            echo "✅ 临时目录中 docker-entrypoint.sh 存在"
            ls -la /tmp/build/docker-entrypoint.sh
          else
            echo "❌ 临时目录中 docker-entrypoint.sh 复制失败！"
            exit 1
          fi

      # 构建并推送
      - name: 构建并推送 Docker 镜像
        timeout-minutes: ${{ matrix.timeout }}
        uses: docker/build-push-action@v5
        with:
          context: /tmp/build
          file: /tmp/build/Dockerfile
          push: true
          tags: ${{ secrets.DOCKER_HUB_USERNAME }}/tweetanalyst:${{ steps.platform_suffix.outputs.suffix }}${{ steps.version_info.outputs.tag_suffix }}
          platforms: ${{ matrix.platform }}
          cache-from: |
            type=gha
            type=local,src=/tmp/.buildx-cache
          cache-to: |
            type=gha,mode=max
            type=local,dest=/tmp/.buildx-cache-new,mode=max
          # 优化构建参数
          build-args: |
            BUILDKIT_STEP_LOG_MAX_SIZE=10485760
            BUILDKIT_STEP_LOG_MAX_SPEED=10485760
            BUILDKIT_PROGRESS=plain
          # 启用并行构建
          provenance: false
          sbom: false

      # 移动缓存以避免缓存增长问题
      - name: 移动构建缓存
        run: |
          rm -rf /tmp/.buildx-cache
          mv /tmp/.buildx-cache-new /tmp/.buildx-cache

  # 创建多架构清单
  create-manifest:
    name: 创建多架构清单
    needs: build-per-platform
    runs-on: ubuntu-latest
    # 手动触发时总是执行

    steps:
      - name: 登录 DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_TOKEN }}

      # 使用手动输入的版本信息
      - name: 提取版本信息
        id: version_info
        run: |
          VERSION="${{ github.event.inputs.version }}"
          if [[ "$VERSION" == "latest" ]]; then
            echo "version=latest" >> $GITHUB_OUTPUT
            echo "tag_suffix=" >> $GITHUB_OUTPUT
            echo "tags=${{ secrets.DOCKER_HUB_USERNAME }}/tweetanalyst:latest" >> $GITHUB_OUTPUT
          else
            echo "version=$VERSION" >> $GITHUB_OUTPUT
            echo "tag_suffix=-$VERSION" >> $GITHUB_OUTPUT
            echo "tags=${{ secrets.DOCKER_HUB_USERNAME }}/tweetanalyst:$VERSION,${{ secrets.DOCKER_HUB_USERNAME }}/tweetanalyst:latest" >> $GITHUB_OUTPUT
          fi
          echo "使用版本: $VERSION"

      # 创建并推送多架构清单
      - name: 创建并推送多架构清单
        run: |
          VERSION="${{ steps.version_info.outputs.version }}"
          DOCKER_CLI_EXPERIMENTAL=enabled

          # 创建并推送多架构清单
          docker manifest create ${{ secrets.DOCKER_HUB_USERNAME }}/tweetanalyst:${VERSION} \
            ${{ secrets.DOCKER_HUB_USERNAME }}/tweetanalyst:amd64${{ steps.version_info.outputs.tag_suffix }} \
            ${{ secrets.DOCKER_HUB_USERNAME }}/tweetanalyst:arm64${{ steps.version_info.outputs.tag_suffix }}

          # 添加架构注释
          docker manifest annotate ${{ secrets.DOCKER_HUB_USERNAME }}/tweetanalyst:${VERSION} \
            ${{ secrets.DOCKER_HUB_USERNAME }}/tweetanalyst:arm64${{ steps.version_info.outputs.tag_suffix }} --arch arm64

          # 推送清单
          docker manifest push ${{ secrets.DOCKER_HUB_USERNAME }}/tweetanalyst:${VERSION}

          # 如果是发布版本，也更新 latest 标签
          if [[ "$VERSION" != "latest" ]]; then
            docker manifest create ${{ secrets.DOCKER_HUB_USERNAME }}/tweetanalyst:latest \
              ${{ secrets.DOCKER_HUB_USERNAME }}/tweetanalyst:amd64${{ steps.version_info.outputs.tag_suffix }} \
              ${{ secrets.DOCKER_HUB_USERNAME }}/tweetanalyst:arm64${{ steps.version_info.outputs.tag_suffix }}

            docker manifest annotate ${{ secrets.DOCKER_HUB_USERNAME }}/tweetanalyst:latest \
              ${{ secrets.DOCKER_HUB_USERNAME }}/tweetanalyst:arm64${{ steps.version_info.outputs.tag_suffix }} --arch arm64

            docker manifest push ${{ secrets.DOCKER_HUB_USERNAME }}/tweetanalyst:latest
          fi
