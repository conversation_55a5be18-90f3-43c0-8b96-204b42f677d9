/**
 * TweetAnalyst全局动画效果
 * 为所有页面添加统一的动画和过渡效果，保持与test页面一致的风格
 */

/* 卡片动画效果 */
.card {
    transition: all 0.3s ease;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* 稳定的卡片，防止抖动 */
.card-footer,
.card-footer *,
.card-footer:hover {
    transform: none !important;
    transition: none !important;
}

.card .card-header {
    border-radius: 0.5rem 0.5rem 0 0;
    font-weight: 600;
}

/* 状态徽章动画 */
.badge {
    font-size: 0.85rem;
    padding: 0.35rem 0.65rem;
    transition: all 0.3s ease;
}

/* 组件状态指示器 */
.component-status {
    padding: 0.75rem;
    border-radius: 0.5rem;
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.component-status:hover {
    transform: translateX(5px);
}

.component-status .status-icon {
    font-size: 1.5rem;
    margin-right: 1rem;
}

.component-status .status-text {
    font-weight: 500;
}

/* 按钮动画效果 */
.btn {
    transition: all 0.3s ease;
    border-radius: 0.5rem;
    position: relative;
    overflow: hidden;
}

.btn:hover {
    transform: translateY(-2px);
}

.btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(-100%);
    transition: transform 0.5s ease;
}

.btn:hover::after {
    transform: translateX(0);
}

/* 表格行动画 */
.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    transform: translateX(5px);
    background-color: rgba(0, 123, 255, 0.05);
}

/* 稳定的表格行，防止抖动 */
.stable-table tbody tr,
.stable-table tbody tr:hover,
.accounts-table tbody tr,
.accounts-table tbody tr:hover {
    transform: none !important;
    transition: background-color 0.3s ease !important;
}

/* 禁用动画的元素 */
.no-animation,
.no-animation * {
    transition: none !important;
    transform: none !important;
    animation: none !important;
}

/* 稳定的表格，防止抖动 */
.stable-table {
    table-layout: fixed;
    width: 100%;
}

.stable-table th,
.stable-table td {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 防止表格行抖动 */
.no-hover-transform tr:hover {
    transform: none !important;
}

/* 稳定的模态框，防止抖动 */
.modal {
    transition: opacity 0.15s linear !important;
    will-change: opacity;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

.modal-backdrop {
    transition: opacity 0.15s linear !important;
    will-change: opacity;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

.modal-dialog {
    transition: transform 0.15s ease-out !important;
    transform: translate(0, -50px) !important;
    will-change: transform;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

.modal.show .modal-dialog {
    transform: translate(0, 0) !important;
}

/* 防止模态框闪烁 */
.modal-stable {
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    perspective: 1000;
    -webkit-perspective: 1000;
}

/* 特别处理删除确认模态框 */
[id^="deleteModal"] {
    transition: opacity 0.15s linear !important;
}

[id^="deleteModal"] .modal-dialog {
    transition: transform 0.15s ease-out !important;
}

/* 覆盖Bootstrap的默认模态框动画 */
.fade {
    transition: opacity 0.15s linear !important;
}

.fade.show {
    opacity: 1;
}

.modal-content .alert,
.modal-content .card,
.modal-content .list-group-item,
.modal-content .btn,
.modal-content .form-control,
.modal-content .form-select,
.modal-content .badge,
.modal-content .bi {
    transition: all 0.15s ease !important;
}

.modal-content .btn:hover,
.modal-content .list-group-item:hover,
.modal-content .card:hover,
.modal-content .form-control:focus,
.modal-content .form-select:focus,
.modal-content .btn:hover .bi {
    transform: translateY(-1px) !important;
}

/* 列表组动画 */
.list-group-item {
    transition: all 0.3s ease;
}

.list-group-item:hover {
    transform: translateX(5px);
    background-color: rgba(0, 123, 255, 0.05);
}

/* 表单控件动画 */
.form-control, .form-select {
    transition: all 0.3s ease;
    border-radius: 0.5rem;
}

.form-control:focus, .form-select:focus {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 123, 255, 0.15);
}

/* 图标动画 */
.bi {
    transition: all 0.3s ease;
}

.btn:hover .bi,
.nav-link:hover .bi,
.list-group-item:hover .bi {
    transform: scale(1.2);
}

/* 进度条动画 */
.progress {
    height: 0.5rem;
    border-radius: 1rem;
    overflow: hidden;
}

/* 脉冲动画 */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.pulse {
    animation: pulse 2s infinite;
}

/* 旋转动画 */
@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.rotate-animation {
    animation: rotate 1s linear;
}

/* 淡入动画 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

/* 滑入动画 */
@keyframes slideInRight {
    from { transform: translateX(50px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .card:hover {
        transform: translateY(-3px);
    }

    .btn:hover {
        transform: translateY(-1px);
    }
}
