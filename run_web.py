#!/usr/bin/env python3
"""
Web应用启动脚本
单独运行Web服务
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from web_app import create_app
from utils.logger import setup_logger

def setup_logging():
    """设置日志"""
    logger = setup_logger('web_app', 'web_app.log')
    return logger

def main():
    """主函数"""
    logger = setup_logging()
    
    logger.info("启动Web应用...")
    
    try:
        # 创建Flask应用
        app = create_app()
        
        # 获取配置
        host = os.getenv('FLASK_HOST', '0.0.0.0')
        port = int(os.getenv('FLASK_PORT', '5000'))
        debug = os.getenv('FLASK_DEBUG', 'false').lower() == 'true'
        
        logger.info(f"Web应用将在 {host}:{port} 启动")
        
        # 启动应用
        app.run(
            host=host,
            port=port,
            debug=debug,
            threaded=True
        )
        
    except Exception as e:
        logger.error(f"Web应用启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
