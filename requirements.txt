# 基础依赖
python-dotenv>=1.0.0
pyyaml>=6.0
requests>=2.28.2
pytz>=2023.3
schedule>=1.2.0

# 数据库迁移支持
alembic>=1.11.0

# LLM支持
langchain-openai>=0.0.1
langchain-core>=0.1.0

# Twitter支持
tweety-ns>=2.2
twikit>=1.7.0

# Web应用
Flask>=2.0.0,<2.4.0
Flask-SQLAlchemy>=3.0.0
Flask-WTF>=1.0.0,<1.2.0
Flask-Login>=0.6.0
Werkzeug>=2.0.0,<2.4.0

# 系统监控
psutil>=5.9.0

# 推送通知
apprise>=1.9.0

# SOCKS代理支持
httpx>=0.24.0

# 可选依赖
# redis>=4.5.1  # 如果需要使用Redis作为缓存，可以取消注释
# beautifulsoup4>=4.12.0  # 如果需要解析HTML，可以取消注释
# gunicorn>=21.2.0  # 如果需要在生产环境中运行，可以取消注释
