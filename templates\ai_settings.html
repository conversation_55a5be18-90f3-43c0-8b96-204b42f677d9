{% extends "base.html" %}

{% block title %}AI设置 - TweetAnalyst{% endblock %}

{% block extra_css %}
<style>
/* 修复模态框定位问题 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1050;
    width: 100%;
    height: 100%;
    overflow: hidden;
    outline: 0;
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: 0.5rem;
    pointer-events: none;
}

@media (min-width: 576px) {
    .modal-dialog {
        max-width: 500px;
        margin: 1.75rem auto;
    }
}

.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 0.3rem;
    outline: 0;
}

/* 确保模态框背景覆盖整个页面 */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1040;
    width: 100vw;
    height: 100vh;
    background-color: #000;
}

.modal-backdrop.fade {
    opacity: 0;
}

.modal-backdrop.show {
    opacity: 0.5;
}

/* 健康状态指示器 */
.health-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 5px;
}

.health-healthy {
    background-color: #28a745;
}

.health-warning {
    background-color: #ffc107;
}

.health-critical {
    background-color: #dc3545;
}

.health-unknown {
    background-color: #6c757d;
}

/* 统计卡片 */
.stat-card {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: transform 0.2s;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    font-size: 2rem;
    opacity: 0.8;
}

/* 表格样式 */
.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.no-hover-transform tbody tr:hover {
    transform: none !important;
}

.stable-table {
    border-collapse: separate;
    border-spacing: 0;
}

.stable-table th, .stable-table td {
    transition: none !important;
}

/* 无动画 */
.no-animation {
    transition: none !important;
    animation: none !important;
}


</style>
{% endblock %}

{% block content %}
<div class="row mb-3">
    <div class="col-12">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('index') }}">首页</a></li>
                <li class="breadcrumb-item active" aria-current="page">AI设置</li>
            </ol>
        </nav>
    </div>
</div>

<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="border-bottom pb-2">
                <i class="bi bi-robot me-2 text-primary"></i>AI设置
            </h2>
            <div>
                <button type="button" id="run-health-check-btn" class="btn btn-outline-info">
                    <i class="bi bi-heart-pulse me-1"></i>运行健康检查
                </button>
            </div>
        </div>
    </div>
</div>

<!-- AI轮询状态卡片 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stat-card border-primary h-100">
            <div class="card-body text-center">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="card-title mb-0">轮询状态</h5>
                    <i class="bi bi-activity stat-icon text-primary"></i>
                </div>
                <div id="polling-status">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card border-success h-100">
            <div class="card-body text-center">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="card-title mb-0">健康检查</h5>
                    <i class="bi bi-heart-pulse stat-icon text-success"></i>
                </div>
                <div id="health-check-stats">
                    <div class="spinner-border text-success" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card border-info h-100">
            <div class="card-body text-center">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="card-title mb-0">缓存状态</h5>
                    <i class="bi bi-speedometer2 stat-icon text-info"></i>
                </div>
                <div id="cache-stats">
                    <div class="spinner-border text-info" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card border-warning h-100">
            <div class="card-body text-center">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="card-title mb-0">批处理状态</h5>
                    <i class="bi bi-collection stat-icon text-warning"></i>
                </div>
                <div id="batch-stats">
                    <div class="spinner-border text-warning" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- AI设置卡片 -->
<div class="card shadow-sm border-info mb-4">
    <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-gear-fill me-2"></i>AI轮询设置
        </h5>
    </div>
    <div class="card-body">
        <form id="ai-polling-form">
            <!-- 基本设置卡片 -->
            <div class="card mb-4 border-primary">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="bi bi-gear-fill me-2"></i>基本设置</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="ai-polling-enabled" name="ai_polling_enabled" {% if ai_polling_enabled %}checked{% endif %} data-config-key="AI_POLLING_ENABLED">
                                <label class="form-check-label" for="ai-polling-enabled">启用AI轮询服务</label>
                            </div>
                            <div class="form-text mb-3">
                                <i class="bi bi-info-circle me-1 text-info"></i>启用后，系统将启动轮询服务，处理缓存和批处理等任务
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="ai-batch-enabled" name="ai_batch_enabled" {% if ai_batch_enabled %}checked{% endif %} data-config-key="AI_BATCH_ENABLED">
                                <label class="form-check-label" for="ai-batch-enabled">启用批处理</label>
                            </div>
                            <div class="form-text mb-3">
                                <i class="bi bi-info-circle me-1 text-info"></i>启用后，系统将合并短时间内的相似请求，提高效率
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 健康检查设置卡片 -->
            <div class="card mb-4 border-info">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="bi bi-heart-pulse-fill me-2"></i>健康检查设置</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="ai-auto-health-check-enabled" name="ai_auto_health_check_enabled" {% if ai_auto_health_check_enabled %}checked{% endif %} data-config-key="AI_AUTO_HEALTH_CHECK_ENABLED">
                                <label class="form-check-label" for="ai-auto-health-check-enabled">启用自动健康检查</label>
                            </div>
                            <div class="form-text mb-3">
                                <i class="bi bi-info-circle me-1 text-info"></i>启用后，系统将定期自动检查AI提供商的健康状态；禁用后，只能手动运行健康检查
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="ai-health-check-interval" class="form-label">健康检查间隔（秒）</label>
                            <input type="number" class="form-control" id="ai-health-check-interval" name="ai_health_check_interval" value="{{ ai_health_check_interval }}" min="10" max="86400" data-config-key="AI_HEALTH_CHECK_INTERVAL" oninput="updateIntervalDisplay()">
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1 text-info"></i>自动健康检查的时间间隔，建议设置较长时间以节省API调用
                            </div>
                            <div id="interval-display" class="mt-2 text-primary">
                                <i class="bi bi-clock me-1"></i><span>相当于 1 小时</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 缓存设置卡片 -->
            <div class="card mb-4 border-success">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="bi bi-database-fill me-2"></i>缓存设置</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="ai-cache-enabled" name="ai_cache_enabled" {% if ai_cache_enabled %}checked{% endif %} data-config-key="AI_CACHE_ENABLED">
                                <label class="form-check-label" for="ai-cache-enabled">启用AI请求缓存</label>
                            </div>
                            <div class="form-text mb-3">
                                <i class="bi bi-info-circle me-1 text-info"></i>启用后，系统将缓存AI请求结果，减少重复请求
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="ai-cache-ttl" class="form-label">缓存有效期（秒）</label>
                            <input type="number" class="form-control" id="ai-cache-ttl" name="ai_cache_ttl" value="{{ ai_cache_ttl }}" min="60" max="86400" data-config-key="AI_CACHE_TTL">
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1 text-info"></i>缓存的有效期，超过此时间将重新请求
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="d-flex justify-content-between align-items-center mt-4">
                <div>
                    <button type="button" id="save-all-settings-btn" class="btn btn-primary btn-lg">
                        <i class="bi bi-save me-1"></i>保存设置
                    </button>
                </div>
                <div class="d-flex gap-2">
                    <button type="button" id="reset-stats-btn" class="btn btn-danger">
                        <i class="bi bi-arrow-counterclockwise me-1"></i>重置统计数据
                    </button>
                    <button type="button" id="clear-cache-btn" class="btn btn-warning">
                        <i class="bi bi-trash me-1"></i>清空缓存
                    </button>
                </div>
            </div>
        </form>
        <div class="mt-3">
            <div id="ai-polling-result" class="alert d-none" style="border: 2px solid; font-weight: 500;"></div>
            <!-- 健康检查结果容器 -->
            <div id="health-check-results" class="mt-3 d-none"></div>
        </div>
    </div>
</div>

<!-- AI提供商管理卡片 -->
<div class="card shadow-sm border-primary mb-4">
    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-robot me-2"></i>AI提供商管理
        </h5>
        <button type="button" class="btn btn-light btn-sm" id="add-provider-btn">
            <i class="bi bi-plus-circle me-1"></i>添加提供商
        </button>
    </div>
    <div class="card-body">
        <div class="alert alert-primary">
            <div class="d-flex">
                <div class="flex-shrink-0">
                    <i class="bi bi-info-circle-fill fs-4 me-2"></i>
                </div>
                <div>
                    <h5 class="alert-heading">多AI提供商功能</h5>
                    <p>系统支持配置多个AI提供商，可以根据优先级轮流使用，提高处理效率并避免单一API的限流问题。</p>
                    <hr>
                    <p class="mb-0">提示：优先级数字越小，优先级越高。当一个AI提供商失败时，系统会自动切换到下一个可用的提供商。</p>
                </div>
            </div>
        </div>

        <!-- AI提供商列表 -->
        <div class="table-responsive">
            <table class="table table-hover table-striped align-middle stable-table no-hover-transform">
                <thead class="table-light">
                    <tr>
                        <th style="width: 15%">名称</th>
                        <th style="width: 20%">模型</th>
                        <th style="width: 8%">优先级</th>
                        <th style="width: 10%">状态</th>
                        <th style="width: 15%">媒体类型</th>
                        <th style="width: 20%">健康状态</th>
                        <th style="width: 12%">操作</th>
                    </tr>
                </thead>
                <tbody id="ai-providers-table-body" class="no-animation">
                    <!-- 这里将通过JavaScript动态加载AI提供商列表 -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 添加AI提供商模态框 -->
<div class="modal fade" id="addProviderModal" tabindex="-1" aria-labelledby="addProviderModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addProviderModalLabel">
                    <i class="bi bi-plus-circle me-2"></i>添加AI提供商
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
                <form id="add-provider-form">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="provider-name" class="form-label fw-bold">提供商名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control form-control-lg" id="provider-name" name="name" required placeholder="例如: OpenAI, X.AI, 通义千问">
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1 text-info"></i>输入一个易于识别的提供商名称
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="provider-priority" class="form-label fw-bold">优先级</label>
                            <input type="number" class="form-control form-control-lg" id="provider-priority" name="priority" value="0" min="0" max="100">
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1 text-info"></i>数字越小优先级越高，系统会优先使用高优先级的提供商
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-12">
                            <label for="provider-api-base" class="form-label fw-bold">API基础URL <span class="text-danger">*</span></label>
                            <div class="input-group input-group-lg">
                                <select class="form-select api-base-select" id="provider-api-base-select" style="max-width: 220px;">
                                    <option value="">自定义...</option>
                                    <!-- 国际AI提供商 -->
                                    <optgroup label="国际AI提供商">
                                        <option value="https://api.openai.com/v1/chat/completions">OpenAI</option>
                                        <option value="https://api.x.ai/v1/chat/completions">X.AI (Grok)</option>
                                        <option value="https://api.groq.com/openai/v1/chat/completions">Groq (推荐)</option>
                                        <option value="https://generativelanguage.googleapis.com/v1/models/gemini-1.5-pro:generateContent">Google Gemini</option>
                                        <option value="https://api.anthropic.com/v1/messages">Anthropic</option>
                                        <option value="https://api.cohere.ai/v1/chat">Cohere</option>
                                        <option value="https://api.mistral.ai/v1/chat/completions">Mistral AI</option>
                                        <option value="https://api.together.xyz/v1/chat/completions">Together AI</option>
                                        <option value="https://api.perplexity.ai/chat/completions">Perplexity AI</option>
                                    </optgroup>
                                    <!-- 中国AI提供商 -->
                                    <optgroup label="中国AI提供商">
                                        <option value="https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation">阿里云通义千问</option>
                                        <option value="https://api.baidu.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions">百度文心一言</option>
                                        <option value="https://xinghuo.xfyun.cn/api/v1/aiui/v1/text_chat">讯飞星火</option>
                                        <option value="https://open.bigmodel.cn/api/paas/v4/chat/completions">智谱AI</option>
                                        <option value="https://api.moonshot.cn/v1/chat/completions">月之暗面</option>
                                        <option value="https://api.minimax.chat/v1/text/chatcompletion_pro">MiniMax</option>
                                        <option value="https://api.sensenova.cn/v1/chat/completions">商汤日日新</option>
                                        <option value="https://maas-api.ml-platform-cn-beijing.volces.com/v1/chat/completions">火山方舟</option>
                                    </optgroup>
                                </select>
                                <input type="text" class="form-control" id="provider-api-base" name="api_base" required placeholder="例如: https://api.openai.com/v1/chat/completions">
                                <span class="input-group-text" data-bs-toggle="tooltip" data-bs-placement="top" title="请输入完整的API URL，系统将完全尊重您的输入，不做任何添加或删减。&#10;例如: https://api.openai.com/v1/chat/completions 或 https://api.example.com/custom/path">
                                    <i class="bi bi-question-circle text-primary"></i>
                                </span>
                            </div>
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1 text-info"></i>选择常用API或输入自定义URL，通常以https://开头
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-12">
                            <label for="provider-model" class="form-label fw-bold">模型名称 <span class="text-danger">*</span></label>
                            <div class="input-group input-group-lg">
                                <select class="form-select model-select" id="provider-model-select" style="max-width: 220px;">
                                    <option value="">选择模型...</option>
                                </select>
                                <input type="text" class="form-control" id="provider-model" name="model" required placeholder="例如: gpt-4o, grok-3-latest, llama3-70b-8192">
                            </div>
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1 text-info"></i>选择常用模型或输入自定义模型，请确保模型名称与API提供商要求一致
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-12">
                            <label for="provider-api-key" class="form-label fw-bold">API密钥 <span class="text-danger">*</span></label>
                            <div class="input-group input-group-lg">
                                <input type="password" class="form-control" id="provider-api-key" name="api_key" required placeholder="输入API密钥">
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('provider-api-key')">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1 text-info"></i>输入从AI提供商获取的API密钥，通常以sk-开头
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-12">
                            <label class="form-label fw-bold">支持的媒体类型</label>
                            <div class="d-flex flex-wrap gap-4 mt-2">
                                <div class="form-check form-check-lg">
                                    <input class="form-check-input" type="checkbox" id="supports-text" name="supports_text" checked>
                                    <label class="form-check-label" for="supports-text">文本</label>
                                </div>
                                <div class="form-check form-check-lg">
                                    <input class="form-check-input" type="checkbox" id="supports-image" name="supports_image">
                                    <label class="form-check-label" for="supports-image">图片</label>
                                </div>
                                <div class="form-check form-check-lg">
                                    <input class="form-check-input" type="checkbox" id="supports-video" name="supports_video">
                                    <label class="form-check-label" for="supports-video">视频</label>
                                </div>
                                <div class="form-check form-check-lg">
                                    <input class="form-check-input" type="checkbox" id="supports-gif" name="supports_gif">
                                    <label class="form-check-label" for="supports-gif">GIF</label>
                                </div>
                            </div>
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1 text-info"></i>选择此提供商支持处理的媒体类型
                            </div>
                        </div>
                    </div>

                    <div class="form-check form-switch form-check-lg mb-3">
                        <input class="form-check-input" type="checkbox" id="provider-is-active" name="is_active" checked>
                        <label class="form-check-label" for="provider-is-active">启用此提供商</label>
                        <div class="form-text">
                            <i class="bi bi-info-circle me-1 text-info"></i>取消勾选可暂时禁用此提供商，而不必删除
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-lg" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-1"></i>取消
                </button>
                <button type="button" class="btn btn-primary btn-lg" id="save-provider-btn">
                    <i class="bi bi-check-circle me-1"></i>保存提供商
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑AI提供商模态框 -->
<div class="modal fade" id="editProviderModal" tabindex="-1" aria-labelledby="editProviderModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editProviderModalLabel">
                    <i class="bi bi-pencil-square me-2"></i>编辑AI提供商
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
                <form id="edit-provider-form">
                    <input type="hidden" id="edit-provider-id" name="id">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="edit-provider-name" class="form-label fw-bold">提供商名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control form-control-lg" id="edit-provider-name" name="name" required placeholder="例如: OpenAI, X.AI, 通义千问">
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1 text-info"></i>输入一个易于识别的提供商名称
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="edit-provider-priority" class="form-label fw-bold">优先级</label>
                            <input type="number" class="form-control form-control-lg" id="edit-provider-priority" name="priority" value="0" min="0" max="100">
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1 text-info"></i>数字越小优先级越高，系统会优先使用高优先级的提供商
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-12">
                            <label for="edit-provider-api-base" class="form-label fw-bold">API基础URL <span class="text-danger">*</span></label>
                            <div class="input-group input-group-lg">
                                <select class="form-select api-base-select" id="edit-provider-api-base-select" style="max-width: 220px;">
                                    <option value="">自定义...</option>
                                    <!-- 国际AI提供商 -->
                                    <optgroup label="国际AI提供商">
                                        <option value="https://api.openai.com/v1/chat/completions">OpenAI</option>
                                        <option value="https://api.x.ai/v1/chat/completions">X.AI (Grok)</option>
                                        <option value="https://api.groq.com/openai/v1/chat/completions">Groq (推荐)</option>
                                        <option value="https://generativelanguage.googleapis.com/v1/models/gemini-1.5-pro:generateContent">Google Gemini</option>
                                        <option value="https://api.anthropic.com/v1/messages">Anthropic</option>
                                        <option value="https://api.cohere.ai/v1/chat">Cohere</option>
                                        <option value="https://api.mistral.ai/v1/chat/completions">Mistral AI</option>
                                        <option value="https://api.together.xyz/v1/chat/completions">Together AI</option>
                                        <option value="https://api.perplexity.ai/chat/completions">Perplexity AI</option>
                                    </optgroup>
                                    <!-- 中国AI提供商 -->
                                    <optgroup label="中国AI提供商">
                                        <option value="https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation">阿里云通义千问</option>
                                        <option value="https://api.baidu.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions">百度文心一言</option>
                                        <option value="https://xinghuo.xfyun.cn/api/v1/aiui/v1/text_chat">讯飞星火</option>
                                        <option value="https://open.bigmodel.cn/api/paas/v4/chat/completions">智谱AI</option>
                                        <option value="https://api.moonshot.cn/v1/chat/completions">月之暗面</option>
                                        <option value="https://api.minimax.chat/v1/text/chatcompletion_pro">MiniMax</option>
                                        <option value="https://api.sensenova.cn/v1/chat/completions">商汤日日新</option>
                                        <option value="https://maas-api.ml-platform-cn-beijing.volces.com/v1/chat/completions">火山方舟</option>
                                    </optgroup>
                                </select>
                                <input type="text" class="form-control" id="edit-provider-api-base" name="api_base" required placeholder="例如: https://api.openai.com/v1/chat/completions">
                                <span class="input-group-text" data-bs-toggle="tooltip" data-bs-placement="top" title="请输入完整的API URL，系统将完全尊重您的输入，不做任何添加或删减。&#10;例如: https://api.openai.com/v1/chat/completions 或 https://api.example.com/custom/path">
                                    <i class="bi bi-question-circle text-primary"></i>
                                </span>
                            </div>
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1 text-info"></i>选择常用API或输入自定义URL，通常以https://开头
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-12">
                            <label for="edit-provider-model" class="form-label fw-bold">模型名称 <span class="text-danger">*</span></label>
                            <div class="input-group input-group-lg">
                                <select class="form-select model-select" id="edit-provider-model-select" style="max-width: 220px;">
                                    <option value="">选择模型...</option>
                                </select>
                                <input type="text" class="form-control" id="edit-provider-model" name="model" required placeholder="例如: gpt-4o, grok-3-latest, llama3-70b-8192">
                            </div>
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1 text-info"></i>选择常用模型或输入自定义模型，请确保模型名称与API提供商要求一致
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-12">
                            <label for="edit-provider-api-key" class="form-label fw-bold">API密钥</label>
                            <div class="input-group input-group-lg">
                                <input type="password" class="form-control" id="edit-provider-api-key" name="api_key" placeholder="留空表示不修改">
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('edit-provider-api-key')">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1 text-info"></i>如不需要修改API密钥，请留空
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-12">
                            <label class="form-label fw-bold">支持的媒体类型</label>
                            <div class="d-flex flex-wrap gap-4 mt-2">
                                <div class="form-check form-check-lg">
                                    <input class="form-check-input" type="checkbox" id="edit-supports-text" name="supports_text">
                                    <label class="form-check-label" for="edit-supports-text">文本</label>
                                </div>
                                <div class="form-check form-check-lg">
                                    <input class="form-check-input" type="checkbox" id="edit-supports-image" name="supports_image">
                                    <label class="form-check-label" for="edit-supports-image">图片</label>
                                </div>
                                <div class="form-check form-check-lg">
                                    <input class="form-check-input" type="checkbox" id="edit-supports-video" name="supports_video">
                                    <label class="form-check-label" for="edit-supports-video">视频</label>
                                </div>
                                <div class="form-check form-check-lg">
                                    <input class="form-check-input" type="checkbox" id="edit-supports-gif" name="supports_gif">
                                    <label class="form-check-label" for="edit-supports-gif">GIF</label>
                                </div>
                            </div>
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1 text-info"></i>选择此提供商支持处理的媒体类型
                            </div>
                        </div>
                    </div>

                    <div class="form-check form-switch form-check-lg mb-3">
                        <input class="form-check-input" type="checkbox" id="edit-provider-is-active" name="is_active">
                        <label class="form-check-label" for="edit-provider-is-active">启用此提供商</label>
                        <div class="form-text">
                            <i class="bi bi-info-circle me-1 text-info"></i>取消勾选可暂时禁用此提供商，而不必删除
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-lg" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-1"></i>取消
                </button>
                <button type="button" class="btn btn-primary btn-lg" id="update-provider-btn">
                    <i class="bi bi-check-circle me-1"></i>更新提供商
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// 密码可见性切换函数
function togglePasswordVisibility(inputId) {
    const input = document.getElementById(inputId);
    const icon = event.currentTarget.querySelector('i');

    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('bi-eye');
        icon.classList.add('bi-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('bi-eye-slash');
        icon.classList.add('bi-eye');
    }
}



// 常用模型数据
const modelsByProvider = {
    // OpenAI
    'https://api.openai.com/v1/chat/completions': [
        { value: 'gpt-4o-2024-05-13', label: 'GPT-4o (最新)' },
        { value: 'gpt-4-turbo-2024-04-09', label: 'GPT-4 Turbo (最新)' },
        { value: 'gpt-4-vision-preview', label: 'GPT-4 Vision' },
        { value: 'gpt-3.5-turbo-0125', label: 'GPT-3.5 Turbo' },
        { value: 'dall-e-3', label: 'DALL-E 3' },
        { value: 'tts-1-hd', label: 'TTS-1-HD (语音合成)' }
    ],
    // Groq
    'https://api.groq.com/openai/v1/chat/completions': [
        { value: 'llama3-70b-8192', label: 'Llama-3-70B-8192 (推荐)' },
        { value: 'llama3-8b-8192', label: 'Llama-3-8B-8192' },
        { value: 'llama-3.1-8b-instant', label: 'Llama-3.1-8B-Instant (快速)' },
        { value: 'gemma2-9b-it', label: 'Gemma-2-9B-IT' },
        { value: 'mixtral-8x7b-32768', label: 'Mixtral-8x7B-32768' },
        { value: 'llama-guard-3-8b', label: 'Llama-Guard-3-8B (安全)' }
    ],
    // X.AI (Grok)
    'https://api.x.ai/v1/chat/completions': [
        { value: 'grok-3-latest', label: 'Grok-3 (最新)' },
        { value: 'grok-3-mini-fast-beta', label: 'Grok-3 Mini Fast' },
        { value: 'grok-3-mini-beta', label: 'Grok-3 Mini' },
        { value: 'grok-2', label: 'Grok-2' }
    ],
    // 旧版Groq API (已弃用)
    'https://api.groq.io/v1/chat/completions': [
        { value: 'llama3-70b-8192', label: 'Llama-3-70B-8192 (推荐)' },
        { value: 'llama3-8b-8192', label: 'Llama-3-8B-8192' },
        { value: 'llama-3.1-8b-instant', label: 'Llama-3.1-8B-Instant (快速)' },
        { value: 'gemma2-9b-it', label: 'Gemma-2-9B-IT' },
        { value: 'mixtral-8x7b-32768', label: 'Mixtral-8x7B-32768' }
    ],
    // Google Gemini
    'https://generativelanguage.googleapis.com/v1/models/gemini-1.5-pro:generateContent': [
        { value: 'gemini-2.5-pro-preview-05-06', label: 'Gemini 2.5 Pro (最新)' },
        { value: 'gemini-2.5-flash-preview-04-17', label: 'Gemini 2.5 Flash (最新)' },
        { value: 'gemini-2.0-flash', label: 'Gemini 2.0 Flash' },
        { value: 'gemini-1.5-pro', label: 'Gemini 1.5 Pro' },
        { value: 'gemini-1.5-flash', label: 'Gemini 1.5 Flash' }
    ],
    // Anthropic
    'https://api.anthropic.com/v1/messages': [
        { value: 'claude-3-7-sonnet-20250219', label: 'Claude 3.7 Sonnet (最新)' },
        { value: 'claude-3-5-sonnet-20241022', label: 'Claude 3.5 Sonnet' },
        { value: 'claude-3-5-haiku-20241022', label: 'Claude 3.5 Haiku' },
        { value: 'claude-3-opus-20240229', label: 'Claude 3 Opus' },
        { value: 'claude-3-sonnet-20240229', label: 'Claude 3 Sonnet' },
        { value: 'claude-3-haiku-20240307', label: 'Claude 3 Haiku' }
    ],
    // Cohere
    'https://api.cohere.ai/v1/chat': [
        { value: 'command-r-plus', label: 'Command R+' },
        { value: 'command-r', label: 'Command R' },
        { value: 'command-light', label: 'Command Light' }
    ],
    // Mistral AI
    'https://api.mistral.ai/v1/chat/completions': [
        { value: 'mistral-saba-24b', label: 'Mistral Saba 24B (最新)' },
        { value: 'mistral-large-latest', label: 'Mistral Large' },
        { value: 'mistral-medium-latest', label: 'Mistral Medium' },
        { value: 'mistral-small-latest', label: 'Mistral Small' }
    ],
    // Together AI
    'https://api.together.xyz/v1/chat/completions': [
        { value: 'meta-llama/llama-4-maverick-17b-128e-instruct', label: 'Llama-4 Maverick 17B (最新)' },
        { value: 'meta-llama/llama-4-scout-17b-16e-instruct', label: 'Llama-4 Scout 17B (最新)' },
        { value: 'meta-llama/Llama-3-70b-chat-hf', label: 'Llama-3 70B' },
        { value: 'meta-llama/Llama-3-8b-chat-hf', label: 'Llama-3 8B' },
        { value: 'mistralai/Mixtral-8x7B-Instruct-v0.1', label: 'Mixtral 8x7B' }
    ],
    // Perplexity AI
    'https://api.perplexity.ai/chat/completions': [
        { value: 'sonar-medium-online', label: 'Sonar Medium Online' },
        { value: 'sonar-small-online', label: 'Sonar Small Online' },
        { value: 'sonar-medium-chat', label: 'Sonar Medium Chat' },
        { value: 'sonar-small-chat', label: 'Sonar Small Chat' }
    ],
    // 阿里云通义千问
    'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation': [
        { value: 'qwen-max', label: '通义千问-Max (最新)' },
        { value: 'qwen-plus', label: '通义千问-Plus' },
        { value: 'qwen-turbo', label: '通义千问-Turbo' },
        { value: 'qwen-max-longcontext', label: '通义千问-Max长文本' }
    ],
    // 百度文心一言
    'https://api.baidu.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions': [
        { value: 'completions', label: '文心一言4.0 (最新)' },
        { value: 'wenxinworkshop/chat/completions', label: '文心一言3.5' }
    ],
    // 讯飞星火
    'https://xinghuo.xfyun.cn/api/v1/aiui/v1/text_chat': [
        { value: 'v4.0', label: '星火认知大模型V4.0 (最新)' },
        { value: 'v3.5', label: '星火认知大模型V3.5' },
        { value: 'v3.0', label: '星火认知大模型V3.0' }
    ],
    // 智谱AI
    'https://open.bigmodel.cn/api/paas/v4/chat/completions': [
        { value: 'glm-4', label: 'GLM-4 (最新)' },
        { value: 'chatglm_turbo', label: 'ChatGLM Turbo' },
        { value: 'chatglm_pro', label: 'ChatGLM Pro' },
        { value: 'chatglm_std', label: 'ChatGLM Std' }
    ],
    // 月之暗面
    'https://api.moonshot.cn/v1/chat/completions': [
        { value: 'moonshot-v1-128k', label: 'Moonshot V1 128K' },
        { value: 'moonshot-v1-32k', label: 'Moonshot V1 32K' },
        { value: 'moonshot-v1-8k', label: 'Moonshot V1 8K' }
    ],
    // MiniMax
    'https://api.minimax.chat/v1/text/chatcompletion_pro': [
        { value: 'abab6.5-chat', label: 'ABAB6.5 Chat (最新)' },
        { value: 'abab6-chat', label: 'ABAB6 Chat' },
        { value: 'abab5.5-chat', label: 'ABAB5.5 Chat' }
    ],
    // 商汤日日新
    'https://api.sensenova.cn/v1/chat/completions': [
        { value: 'nova-ptc-xl-v2', label: '日日新XL V2 (最新)' },
        { value: 'nova-ptc-xl-v1', label: '日日新XL' },
        { value: 'nova-ptc-xs-v1', label: '日日新XS' }
    ],
    // 火山方舟
    'https://maas-api.ml-platform-cn-beijing.volces.com/v1/chat/completions': [
        { value: 'volcengine-2024', label: '方舟2024 (最新)' },
        { value: 'volcengine-2023-turbo', label: '方舟2023 Turbo' },
        { value: 'volcengine-2023', label: '方舟2023' }
    ]
};

// API基础URL和模型选择处理
document.addEventListener('DOMContentLoaded', function() {
    // 初始化所有工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });



    // 添加提供商模态框
    const addProviderApiBaseSelect = document.getElementById('provider-api-base-select');
    const addProviderApiBaseInput = document.getElementById('provider-api-base');
    const addProviderModelSelect = document.getElementById('provider-model-select');
    const addProviderModelInput = document.getElementById('provider-model');

    if (addProviderApiBaseSelect && addProviderApiBaseInput) {
        addProviderApiBaseSelect.addEventListener('change', function() {
            if (this.value) {
                addProviderApiBaseInput.value = this.value;

                // 更新模型下拉列表
                updateModelOptions(addProviderModelSelect, this.value);
            }
        });
    }

    if (addProviderModelSelect && addProviderModelInput) {
        addProviderModelSelect.addEventListener('change', function() {
            if (this.value) {
                addProviderModelInput.value = this.value;
            }
        });
    }

    // 编辑提供商模态框
    const editProviderApiBaseSelect = document.getElementById('edit-provider-api-base-select');
    const editProviderApiBaseInput = document.getElementById('edit-provider-api-base');
    const editProviderModelSelect = document.getElementById('edit-provider-model-select');
    const editProviderModelInput = document.getElementById('edit-provider-model');

    if (editProviderApiBaseSelect && editProviderApiBaseInput) {
        editProviderApiBaseSelect.addEventListener('change', function() {
            if (this.value) {
                editProviderApiBaseInput.value = this.value;

                // 更新模型下拉列表
                updateModelOptions(editProviderModelSelect, this.value);
            }
        });

        // 当编辑模态框打开时，根据当前值选择下拉列表项
        document.getElementById('editProviderModal').addEventListener('show.bs.modal', function() {
            const currentApiBaseValue = editProviderApiBaseInput.value;
            const currentModelValue = editProviderModelInput.value;

            // 重置下拉列表选择
            editProviderApiBaseSelect.value = '';

            // 查找匹配的API基础URL选项
            for (let i = 0; i < editProviderApiBaseSelect.options.length; i++) {
                if (editProviderApiBaseSelect.options[i].value === currentApiBaseValue) {
                    editProviderApiBaseSelect.value = currentApiBaseValue;

                    // 更新模型下拉列表
                    updateModelOptions(editProviderModelSelect, currentApiBaseValue);

                    // 选择当前模型
                    editProviderModelSelect.value = '';
                    for (let j = 0; j < editProviderModelSelect.options.length; j++) {
                        if (editProviderModelSelect.options[j].value === currentModelValue) {
                            editProviderModelSelect.value = currentModelValue;
                            break;
                        }
                    }

                    break;
                }
            }
        });
    }

    if (editProviderModelSelect && editProviderModelInput) {
        editProviderModelSelect.addEventListener('change', function() {
            if (this.value) {
                editProviderModelInput.value = this.value;
            }
        });
    }

    // 更新模型下拉列表选项
    function updateModelOptions(selectElement, apiBaseUrl) {
        // 清空现有选项，只保留第一个"选择模型..."选项
        while (selectElement.options.length > 1) {
            selectElement.remove(1);
        }

        // 获取对应API基础URL的模型列表
        const models = modelsByProvider[apiBaseUrl] || [];

        // 添加模型选项
        for (const model of models) {
            const option = document.createElement('option');
            option.value = model.value;
            option.textContent = model.label;
            selectElement.appendChild(option);
        }
    }
});
</script>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/ai_settings.js') }}"></script>
{% endblock %}
