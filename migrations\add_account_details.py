"""
添加社交账号详细信息字段的迁移脚本
"""

from alembic import op
import sqlalchemy as sa
from datetime import datetime

# 修订版本ID
revision = '002_add_account_details'
down_revision = None  # 如果是第一个迁移脚本，则为None
branch_labels = None
depends_on = None


def upgrade():
    """升级数据库"""
    # 添加新字段
    op.add_column('social_account', sa.Column('display_name', sa.String(100), nullable=True))
    op.add_column('social_account', sa.Column('bio', sa.Text(), nullable=True))
    op.add_column('social_account', sa.Column('verified', sa.<PERSON>(), default=False))
    op.add_column('social_account', sa.Column('followers_count', sa.Integer(), default=0))
    op.add_column('social_account', sa.Column('following_count', sa.Integer(), default=0))
    op.add_column('social_account', sa.Column('join_date', sa.DateTime(), nullable=True))
    op.add_column('social_account', sa.Column('location', sa.String(100), nullable=True))
    op.add_column('social_account', sa.Column('website', sa.String(200), nullable=True))
    op.add_column('social_account', sa.Column('profession', sa.String(100), nullable=True))


def downgrade():
    """降级数据库"""
    # 删除添加的字段
    op.drop_column('social_account', 'display_name')
    op.drop_column('social_account', 'bio')
    op.drop_column('social_account', 'verified')
    op.drop_column('social_account', 'followers_count')
    op.drop_column('social_account', 'following_count')
    op.drop_column('social_account', 'join_date')
    op.drop_column('social_account', 'location')
    op.drop_column('social_account', 'website')
    op.drop_column('social_account', 'profession')
