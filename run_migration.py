#!/usr/bin/env python3
"""
数据库迁移脚本
用于执行数据库迁移和初始化
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from web_app import create_app, db
from migrations.db_migrations import run_all_migrations

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('migration.log')
        ]
    )

def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("开始数据库迁移...")
    
    try:
        # 创建Flask应用
        app = create_app()
        
        with app.app_context():
            # 创建所有表
            logger.info("创建数据库表...")
            db.create_all()
            
            # 运行迁移
            logger.info("执行数据库迁移...")
            run_all_migrations()
            
            logger.info("数据库迁移完成!")
            
    except Exception as e:
        logger.error(f"数据库迁移失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
 --------原始代码


"""
执行数据库迁移脚本

这个脚本是数据库迁移的入口点，它调用统一的数据库迁移管理脚本执行所有迁移操作。
"""

import sys
from datetime import datetime

# 使用统一的日志管理模块
from utils.logger import get_logger

# 创建日志记录器
logger = get_logger('migration_runner')

def main():
    """
    主函数
    """
    logger.info("开始执行数据库迁移")
    start_time = datetime.now()

    try:
        # 导入统一迁移脚本
        from migrations.db_migrations import run_all_migrations

        # 执行所有迁移
        success = run_all_migrations()

        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        if success:
            logger.info(f"所有迁移成功完成，耗时 {duration:.2f} 秒")
        else:
            logger.error(f"迁移过程中出现错误，耗时 {duration:.2f} 秒")
            sys.exit(1)
    except Exception as e:
        logger.error(f"执行迁移时出错: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
