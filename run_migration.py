#!/usr/bin/env python3
"""
数据库迁移脚本
用于执行数据库迁移和初始化
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from web_app import create_app, db
from migrations.db_migrations import run_all_migrations

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('migration.log')
        ]
    )

def main():
    """主函数"""
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("开始数据库迁移...")
    
    try:
        # 创建Flask应用
        app = create_app()
        
        with app.app_context():
            # 创建所有表
            logger.info("创建数据库表...")
            db.create_all()
            
            # 运行迁移
            logger.info("执行数据库迁移...")
            run_all_migrations()
            
            logger.info("数据库迁移完成!")
            
    except Exception as e:
        logger.error(f"数据库迁移失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
