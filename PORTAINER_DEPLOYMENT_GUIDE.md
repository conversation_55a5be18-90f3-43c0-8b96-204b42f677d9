# Portainer部署指南

## 🚀 快速部署步骤

### 步骤1: 准备Docker Compose配置

在Portainer中创建新的Stack，使用以下配置：

```yaml
version: '3.8'

# 本项目由一个写不出一行代码的白痴在Augment Code (https://augmentcode.com) 的强大帮助下完成

services:
  tweetAnalyst:
    image: ${DOCKER_IMAGE:-your_dockerhub_username/tweetanalyst:latest}
    # 如果需要从本地构建，取消下面这行的注释，并注释掉上面的image行
    # build: .
    container_name: tweetAnalyst
    ports:
      - "5000:5000"
    volumes:
      - ./data:/data
      - ./config:/app/config
      - ./logs:/app/logs
    environment:
      # 基础配置
      - FLASK_SECRET_KEY=your_random_secret_key_please_change_this
      - DATABASE_PATH=/data/tweetAnalyst.db
      - LOG_DIR=/app/logs
      - FIRST_LOGIN=${FIRST_LOGIN:-auto}

      # LLM API配置（可以在.env文件中设置，或者在这里直接设置）
      - LLM_API_KEY=${LLM_API_KEY}
      - LLM_API_MODEL=${LLM_API_MODEL:-grok-3-mini-beta}
      - LLM_API_BASE=${LLM_API_BASE:-https://api.x.ai/v1}

      # 代理配置（如果需要）
      - HTTP_PROXY=${HTTP_PROXY}
      - HTTPS_PROXY=${HTTPS_PROXY}

      # 推送配置（可选）
      - APPRISE_URLS=${APPRISE_URLS}

      # 自动抓取配置
      - AUTO_FETCH_ENABLED=${AUTO_FETCH_ENABLED:-false}
      - SCHEDULER_INTERVAL_MINUTES=${SCHEDULER_INTERVAL_MINUTES:-30}

      # 自动回复配置
      - ENABLE_AUTO_REPLY=${ENABLE_AUTO_REPLY:-false}
      - AUTO_REPLY_PROMPT=${AUTO_REPLY_PROMPT}

      # 数据库自动清理配置
      - DB_AUTO_CLEAN_ENABLED=${DB_AUTO_CLEAN_ENABLED:-false}
      - DB_AUTO_CLEAN_TIME=${DB_AUTO_CLEAN_TIME:-03:00}
      - DB_CLEAN_BY_COUNT=${DB_CLEAN_BY_COUNT:-false}
      - DB_MAX_RECORDS_PER_ACCOUNT=${DB_MAX_RECORDS_PER_ACCOUNT:-100}
      - DB_RETENTION_DAYS=${DB_RETENTION_DAYS:-30}
      - DB_CLEAN_IRRELEVANT_ONLY=${DB_CLEAN_IRRELEVANT_ONLY:-true}

    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  default:
    name: tweetanalyst_network
```

### 步骤2: 设置环境变量

在Portainer的Stack配置中，添加以下环境变量：

```bash
# 必需配置
DOCKER_IMAGE=your_dockerhub_username/tweetanalyst:latest
LLM_API_KEY=your_api_key_here
FLASK_SECRET_KEY=your_random_secret_key_change_this

# 可选配置
LLM_API_MODEL=grok-3-mini-beta
LLM_API_BASE=https://api.x.ai/v1
APPRISE_URLS=tgram://your_bot_token/your_chat_id
HTTP_PROXY=http://your.proxy:port
AUTO_FETCH_ENABLED=false
```

### 步骤3: 创建数据卷

确保在Portainer中创建或映射以下目录：
- `./data` - 数据库文件存储
- `./config` - 配置文件存储  
- `./logs` - 日志文件存储

## 🔧 故障排除

### 问题1: "no such file or directory: docker-entrypoint.sh"

**原因**: 镜像构建时启动脚本文件缺失或权限问题

**解决方案**:
1. 检查使用的镜像标签是否正确
2. 尝试使用最新的镜像版本
3. 如果问题持续，重新触发GitHub Actions构建

```bash
# 在Portainer中查看容器日志
docker logs container_name

# 检查镜像内容
docker run --rm -it your_dockerhub_username/tweetanalyst:latest ls -la /app/
```

### 问题2: 容器启动后立即退出

**可能原因**:
- 环境变量配置错误
- 数据卷权限问题
- 端口冲突

**解决方案**:
1. 检查环境变量配置
2. 确保数据目录有正确权限
3. 检查端口5000是否被占用

### 问题3: 数据库连接失败

**解决方案**:
1. 确保数据卷正确映射到 `/data`
2. 检查 `DATABASE_PATH` 环境变量
3. 确保数据目录有写入权限

### 问题4: 网络连接问题

**解决方案**:
1. 配置正确的代理设置
2. 检查防火墙规则
3. 确保容器可以访问外部网络

## 📋 部署检查清单

### 部署前检查:
- [ ] 确认镜像名称和标签正确
- [ ] 设置必需的环境变量（LLM_API_KEY, FLASK_SECRET_KEY）
- [ ] 创建数据存储目录
- [ ] 配置网络和端口映射

### 部署后检查:
- [ ] 容器成功启动
- [ ] 健康检查通过
- [ ] Web界面可访问 (http://localhost:5000)
- [ ] 日志无错误信息
- [ ] 数据库文件正确创建

## 🛠️ 高级配置

### 使用外部数据库
如果需要使用外部数据库（如PostgreSQL），修改环境变量：

```yaml
environment:
  - DATABASE_URL=postgresql://user:password@host:port/database
```

### 集群部署
对于高可用部署，可以配置多个实例：

```yaml
deploy:
  replicas: 3
  update_config:
    parallelism: 1
    delay: 10s
  restart_policy:
    condition: on-failure
```

### 监控配置
添加监控和日志收集：

```yaml
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

## 📞 获取帮助

如果遇到问题：
1. 查看容器日志: `docker logs container_name`
2. 检查系统状态: 访问 http://localhost:5000/system_test
3. 查看GitHub Issues: [项目Issues页面]
4. 重新构建镜像: 在GitHub Actions中手动触发工作流

## 🔄 更新部署

更新到新版本：
1. 在Portainer中停止当前Stack
2. 更新镜像标签到新版本
3. 重新部署Stack
4. 检查更新日志确认成功
