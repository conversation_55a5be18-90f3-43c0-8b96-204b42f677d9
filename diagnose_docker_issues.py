#!/usr/bin/env python3
"""
Docker部署问题诊断脚本
"""

import os
import subprocess
import json
from pathlib import Path

def run_command(cmd, capture_output=True):
    """运行命令并返回结果"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=capture_output, text=True)
        return result.returncode, result.stdout, result.stderr
    except Exception as e:
        return -1, "", str(e)

def check_file_permissions():
    """检查关键文件的权限"""
    print("🔍 检查文件权限...")
    
    files_to_check = [
        'docker-entrypoint.sh',
        'Dockerfile',
        'docker-compose.yml',
        'requirements.txt',
        'run_all.py',
        'main.py',
        'web_app.py'
    ]
    
    issues = []
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            stat = os.stat(file_path)
            permissions = oct(stat.st_mode)[-3:]
            print(f"  ✅ {file_path}: 权限 {permissions}")
            
            # 检查docker-entrypoint.sh是否可执行
            if file_path == 'docker-entrypoint.sh':
                if not os.access(file_path, os.X_OK):
                    issues.append(f"{file_path} 不可执行")
                    print(f"  ❌ {file_path} 不可执行")
                else:
                    print(f"  ✅ {file_path} 可执行")
        else:
            issues.append(f"{file_path} 不存在")
            print(f"  ❌ {file_path} 不存在")
    
    return issues

def check_docker_environment():
    """检查Docker环境"""
    print("\n🐳 检查Docker环境...")
    
    issues = []
    
    # 检查Docker是否安装
    code, stdout, stderr = run_command("docker --version")
    if code == 0:
        print(f"  ✅ Docker版本: {stdout.strip()}")
    else:
        issues.append("Docker未安装或不可用")
        print(f"  ❌ Docker未安装: {stderr}")
    
    # 检查Docker Compose是否安装
    code, stdout, stderr = run_command("docker-compose --version")
    if code == 0:
        print(f"  ✅ Docker Compose版本: {stdout.strip()}")
    else:
        # 尝试新版本命令
        code, stdout, stderr = run_command("docker compose version")
        if code == 0:
            print(f"  ✅ Docker Compose版本: {stdout.strip()}")
        else:
            issues.append("Docker Compose未安装或不可用")
            print(f"  ❌ Docker Compose未安装: {stderr}")
    
    # 检查Docker服务状态
    code, stdout, stderr = run_command("docker info")
    if code == 0:
        print("  ✅ Docker服务正常运行")
    else:
        issues.append("Docker服务未运行")
        print(f"  ❌ Docker服务未运行: {stderr}")
    
    return issues

def check_docker_compose_config():
    """检查docker-compose.yml配置"""
    print("\n📋 检查docker-compose.yml配置...")
    
    issues = []
    
    if not os.path.exists('docker-compose.yml'):
        issues.append("docker-compose.yml文件不存在")
        print("  ❌ docker-compose.yml文件不存在")
        return issues
    
    # 验证docker-compose.yml语法
    code, stdout, stderr = run_command("docker-compose config")
    if code == 0:
        print("  ✅ docker-compose.yml语法正确")
    else:
        issues.append(f"docker-compose.yml语法错误: {stderr}")
        print(f"  ❌ docker-compose.yml语法错误: {stderr}")
    
    # 检查镜像配置
    with open('docker-compose.yml', 'r', encoding='utf-8') as f:
        content = f.read()
        if 'your_dockerhub_username' in content:
            issues.append("docker-compose.yml中包含占位符用户名")
            print("  ❌ 检测到占位符用户名 'your_dockerhub_username'")
        else:
            print("  ✅ 镜像配置正确")
    
    return issues

def check_dockerfile():
    """检查Dockerfile"""
    print("\n🐋 检查Dockerfile...")
    
    issues = []
    
    if not os.path.exists('Dockerfile'):
        issues.append("Dockerfile不存在")
        print("  ❌ Dockerfile不存在")
        return issues
    
    with open('Dockerfile', 'r', encoding='utf-8') as f:
        content = f.read()
        
        # 检查关键指令
        if 'COPY . .' in content:
            print("  ✅ 包含文件复制指令")
        else:
            issues.append("Dockerfile缺少文件复制指令")
            print("  ❌ 缺少文件复制指令")
        
        if 'chmod +x /app/docker-entrypoint.sh' in content:
            print("  ✅ 包含启动脚本权限设置")
        else:
            issues.append("Dockerfile缺少启动脚本权限设置")
            print("  ❌ 缺少启动脚本权限设置")
        
        if 'CMD ["/app/docker-entrypoint.sh"]' in content:
            print("  ✅ 启动命令正确")
        else:
            issues.append("Dockerfile启动命令不正确")
            print("  ❌ 启动命令不正确")
    
    return issues

def check_existing_containers():
    """检查现有容器"""
    print("\n📦 检查现有容器...")
    
    issues = []
    
    # 检查运行中的容器
    code, stdout, stderr = run_command("docker ps")
    if code == 0:
        if 'tweetanalyst' in stdout.lower():
            print("  ⚠️ 发现运行中的tweetanalyst容器")
            print("  建议先停止现有容器: docker-compose down")
        else:
            print("  ✅ 没有运行中的tweetanalyst容器")
    
    # 检查所有容器（包括停止的）
    code, stdout, stderr = run_command("docker ps -a")
    if code == 0:
        if 'tweetanalyst' in stdout.lower():
            print("  ⚠️ 发现已存在的tweetanalyst容器")
            print("  建议清理: docker-compose down && docker system prune -f")
    
    return issues

def check_network_issues():
    """检查网络问题"""
    print("\n🌐 检查网络配置...")
    
    issues = []
    
    # 检查Docker网络
    code, stdout, stderr = run_command("docker network ls")
    if code == 0:
        if 'tweetanalyst' in stdout:
            print("  ⚠️ 发现已存在的tweetanalyst网络")
            print("  建议清理: docker network prune -f")
        else:
            print("  ✅ 网络配置正常")
    
    return issues

def generate_fix_script():
    """生成修复脚本"""
    print("\n🛠️ 生成修复脚本...")
    
    fix_script = """#!/bin/bash
# TweetAnalyst Docker部署修复脚本

echo "🔧 开始修复Docker部署问题..."

# 1. 停止并清理现有容器
echo "1. 清理现有容器和网络..."
docker-compose down 2>/dev/null || true
docker system prune -f
docker network prune -f

# 2. 修复文件权限
echo "2. 修复文件权限..."
chmod +x docker-entrypoint.sh
chmod 644 docker-compose.yml
chmod 644 Dockerfile
chmod 644 requirements.txt

# 3. 检查并修复docker-compose.yml
echo "3. 检查docker-compose.yml配置..."
if grep -q "your_dockerhub_username" docker-compose.yml; then
    echo "⚠️ 检测到占位符用户名，请手动修改docker-compose.yml中的镜像名称"
    echo "   将 'your_dockerhub_username/tweetanalyst:latest' 替换为实际的镜像名称"
fi

# 4. 验证配置
echo "4. 验证配置..."
docker-compose config

# 5. 重新构建并启动
echo "5. 重新构建并启动..."
docker-compose up --build -d

echo "✅ 修复完成！"
echo "📝 请查看日志: docker-compose logs -f"
"""
    
    with open('fix_docker_deployment.sh', 'w', encoding='utf-8') as f:
        f.write(fix_script)
    
    os.chmod('fix_docker_deployment.sh', 0o755)
    print("  ✅ 已生成修复脚本: fix_docker_deployment.sh")

def main():
    """主函数"""
    print("🔍 TweetAnalyst Docker部署问题诊断")
    print("=" * 50)
    
    all_issues = []
    
    # 执行各项检查
    all_issues.extend(check_file_permissions())
    all_issues.extend(check_docker_environment())
    all_issues.extend(check_docker_compose_config())
    all_issues.extend(check_dockerfile())
    all_issues.extend(check_existing_containers())
    all_issues.extend(check_network_issues())
    
    # 生成修复脚本
    generate_fix_script()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 诊断总结")
    
    if all_issues:
        print(f"❌ 发现 {len(all_issues)} 个问题:")
        for i, issue in enumerate(all_issues, 1):
            print(f"  {i}. {issue}")
        
        print("\n🛠️ 建议修复步骤:")
        print("1. 运行修复脚本: bash fix_docker_deployment.sh")
        print("2. 如果仍有问题，请手动检查上述问题列表")
        print("3. 查看详细日志: docker-compose logs -f")
    else:
        print("✅ 没有发现明显问题")
        print("🚀 可以尝试重新部署: docker-compose up -d")

if __name__ == "__main__":
    main()
