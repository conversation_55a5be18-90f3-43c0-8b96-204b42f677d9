#!/usr/bin/env python3
"""
定时任务调度器启动脚本
单独运行定时任务调度服务
"""

import os
import sys
import logging
import signal
import time
import schedule
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from main import main as run_main_task
from utils.logger import setup_logger

def setup_logging():
    """设置日志"""
    logger = setup_logger('scheduler', 'scheduler.log')
    return logger

def signal_handler(signum, frame):
    """信号处理器"""
    logger = logging.getLogger('scheduler')
    logger.info(f"接收到信号 {signum}，正在关闭调度器...")
    sys.exit(0)

def job_wrapper():
    """任务包装器"""
    logger = logging.getLogger('scheduler')
    try:
        logger.info("开始执行定时任务...")
        run_main_task()
        logger.info("定时任务执行完成")
    except Exception as e:
        logger.error(f"定时任务执行失败: {e}")

def main():
    """主函数"""
    logger = setup_logging()
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 获取调度间隔（分钟）
    interval_minutes = int(os.getenv('SCHEDULER_INTERVAL_MINUTES', '30'))
    
    logger.info(f"启动定时任务调度器，间隔: {interval_minutes} 分钟")
    
    # 设置定时任务
    schedule.every(interval_minutes).minutes.do(job_wrapper)
    
    # 立即执行一次
    logger.info("立即执行一次任务...")
    job_wrapper()
    
    logger.info("定时任务调度器已启动，按 Ctrl+C 停止")
    
    try:
        # 运行调度器
        while True:
            schedule.run_pending()
            time.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("接收到中断信号，正在停止...")
    except Exception as e:
        logger.error(f"调度器运行失败: {e}")
        sys.exit(1)
    finally:
        logger.info("定时任务调度器已停止")

if __name__ == "__main__":
    main()
