{% extends "base.html" %}

{% block title %}推送通知管理{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <!-- 选项卡导航 -->
            <ul class="nav nav-tabs mb-3" id="pushTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="history-tab" data-bs-toggle="tab" data-bs-target="#history" type="button" role="tab" aria-controls="history" aria-selected="true">
                        <i class="bi bi-clock-history me-1"></i>推送历史
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="config-tab" data-bs-toggle="tab" data-bs-target="#config" type="button" role="tab" aria-controls="config" aria-selected="false">
                        <i class="bi bi-gear me-1"></i>推送配置
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="test-tab" data-bs-toggle="tab" data-bs-target="#test" type="button" role="tab" aria-controls="test" aria-selected="false">
                        <i class="bi bi-check2-circle me-1"></i>推送测试
                    </button>
                </li>
            </ul>

            <!-- 选项卡内容 -->
            <div class="tab-content" id="pushTabsContent">
                <!-- 推送历史选项卡 -->
                <div class="tab-pane fade show active" id="history" role="tabpanel" aria-labelledby="history-tab">
                    <div class="card mb-4">
                        <div class="card-header pb-0">
                            <div class="row">
                                <div class="col-6">
                                    <h6>推送历史记录</h6>
                                </div>
                                <div class="col-6 text-end">
                                    <button class="btn btn-sm btn-primary" id="refresh-btn">
                                        <i class="bi bi-arrow-clockwise me-1"></i>刷新
                                    </button>
                                    <button class="btn btn-sm btn-danger" id="clean-btn" data-bs-toggle="modal" data-bs-target="#cleanModal">
                                        <i class="bi bi-trash me-1"></i>清理
                                    </button>
                                </div>
                            </div>
                        </div>

                <!-- 统计信息卡片 -->
                <div class="card-body pt-0 pb-2">
                    <div class="row mt-3">
                        <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
                            <div class="card">
                                <div class="card-body p-3">
                                    <div class="row">
                                        <div class="col-8">
                                            <div class="numbers">
                                                <p class="text-sm mb-0 text-uppercase font-weight-bold">待处理</p>
                                                <h5 class="font-weight-bolder" id="pending-count">0</h5>
                                                <p class="mb-0 text-sm">
                                                    <span class="text-success text-sm font-weight-bolder" id="retrying-count">0</span>
                                                    <span class="text-sm">正在重试</span>
                                                </p>
                                            </div>
                                        </div>
                                        <div class="col-4 text-end">
                                            <div class="icon icon-shape bg-gradient-primary shadow-primary text-center rounded-circle">
                                                <i class="bi bi-hourglass-split text-lg opacity-10"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
                            <div class="card">
                                <div class="card-body p-3">
                                    <div class="row">
                                        <div class="col-8">
                                            <div class="numbers">
                                                <p class="text-sm mb-0 text-uppercase font-weight-bold">成功</p>
                                                <h5 class="font-weight-bolder" id="success-count">0</h5>
                                                <p class="mb-0 text-sm">
                                                    <span class="text-success text-sm font-weight-bolder" id="recent-count">0</span>
                                                    <span class="text-sm">最近24小时</span>
                                                </p>
                                            </div>
                                        </div>
                                        <div class="col-4 text-end">
                                            <div class="icon icon-shape bg-gradient-success shadow-success text-center rounded-circle">
                                                <i class="bi bi-check-circle text-lg opacity-10"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
                            <div class="card">
                                <div class="card-body p-3">
                                    <div class="row">
                                        <div class="col-8">
                                            <div class="numbers">
                                                <p class="text-sm mb-0 text-uppercase font-weight-bold">失败</p>
                                                <h5 class="font-weight-bolder" id="failed-count">0</h5>
                                                <p class="mb-0 text-sm">
                                                    <span class="text-danger text-sm font-weight-bolder">需要手动重试</span>
                                                </p>
                                            </div>
                                        </div>
                                        <div class="col-4 text-end">
                                            <div class="icon icon-shape bg-gradient-danger shadow-danger text-center rounded-circle">
                                                <i class="bi bi-x-circle text-lg opacity-10"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-sm-6">
                            <div class="card">
                                <div class="card-body p-3">
                                    <div class="row">
                                        <div class="col-8">
                                            <div class="numbers">
                                                <p class="text-sm mb-0 text-uppercase font-weight-bold">队列状态</p>
                                                <h5 class="font-weight-bolder" id="worker-status">未知</h5>
                                                <p class="mb-0 text-sm">
                                                    <span class="text-info text-sm font-weight-bolder" id="worker-interval">30秒</span>
                                                    <span class="text-sm">处理间隔</span>
                                                </p>
                                            </div>
                                        </div>
                                        <div class="col-4 text-end">
                                            <div class="icon icon-shape bg-gradient-info shadow-info text-center rounded-circle">
                                                <i class="bi bi-gear text-lg opacity-10"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 筛选工具栏 -->
                <div class="card-body pt-0 pb-2">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary btn-sm status-filter active" data-status="all">全部</button>
                                <button type="button" class="btn btn-outline-primary btn-sm status-filter" data-status="active">待处理</button>
                                <button type="button" class="btn btn-outline-primary btn-sm status-filter" data-status="success">成功</button>
                                <button type="button" class="btn btn-outline-primary btn-sm status-filter" data-status="failed">失败</button>
                            </div>
                        </div>
                        <div class="col-md-6 text-end">
                            <div class="input-group input-group-sm">
                                <input type="text" class="form-control" placeholder="搜索..." id="search-input">
                                <button class="btn btn-outline-primary" type="button" id="search-btn">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 通知列表 -->
                <div class="card-body px-0 pt-0 pb-2">
                    <div class="table-responsive p-0">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">ID</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">标题/内容</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">状态</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">成功/总数</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">创建时间</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">发送时间</th>
                                    <th class="text-secondary opacity-7"></th>
                                </tr>
                            </thead>
                            <tbody id="notifications-table-body">
                                <!-- 通知列表将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 分页 -->
                <div class="card-footer">
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center" id="pagination">
                            <!-- 分页将通过JavaScript动态加载 -->
                        </ul>
                    </nav>
                        </div>
                    </div>
                </div>

                <!-- 推送配置选项卡 -->
                <div class="tab-pane fade" id="config" role="tabpanel" aria-labelledby="config-tab">
                    <div class="card mb-4">
                        <div class="card-header pb-0">
                            <h6>推送服务配置</h6>
                        </div>
                        <div class="card-body">
                            <form id="notification-form">
                                <div class="mb-3">
                                    <label for="apprise-urls" class="form-label">
                                        <i class="bi bi-link-45deg me-1 text-danger"></i>Apprise URLs
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="bi bi-code-slash"></i></span>
                                        <textarea class="form-control" id="apprise-urls" rows="5" placeholder="输入Apprise URLs，每行一个">{{ apprise_urls }}</textarea>
                                    </div>
                                    <div class="form-text">
                                        <p>支持的URL格式示例：</p>
                                        <ul>
                                            <li>Telegram: <code>tgram://BOT_TOKEN/CHAT_ID</code></li>
                                            <li>Discord: <code>discord://WEBHOOK_ID/WEBHOOK_TOKEN</code></li>
                                            <li>Slack: <code>slack://TOKEN/CHANNEL</code></li>
                                            <li>企业微信: <code>wxteams://CORPID/SECRET/AGENTID</code></li>
                                            <li>更多格式请参考 <a href="https://github.com/caronc/apprise/wiki" target="_blank">Apprise Wiki</a></li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="push-queue-enabled" class="form-label">
                                        <i class="bi bi-toggle-on me-1 text-success"></i>推送队列
                                    </label>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="push-queue-enabled" {% if push_queue_enabled %}checked{% endif %}>
                                        <label class="form-check-label" for="push-queue-enabled">启用推送队列</label>
                                    </div>
                                    <div class="form-text">启用后，推送请求将先进入队列，然后由后台处理器处理，提高可靠性</div>
                                </div>
                                <div class="mb-3">
                                    <label for="push-queue-interval" class="form-label">
                                        <i class="bi bi-clock me-1 text-primary"></i>队列处理间隔（秒）
                                    </label>
                                    <input type="number" class="form-control" id="push-queue-interval" value="{{ push_queue_interval }}" min="5" max="300">
                                    <div class="form-text">队列处理器每隔多少秒处理一次队列，建议值：30秒</div>
                                </div>
                                <div class="mb-3">
                                    <label for="push-max-attempts" class="form-label">
                                        <i class="bi bi-arrow-repeat me-1 text-warning"></i>最大重试次数
                                    </label>
                                    <input type="number" class="form-control" id="push-max-attempts" value="{{ push_max_attempts }}" min="1" max="10">
                                    <div class="form-text">推送失败时最多重试几次，建议值：3次</div>
                                </div>
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <button type="button" class="btn btn-primary" id="save-notification-btn">
                                        <i class="bi bi-save me-1"></i>保存配置
                                    </button>
                                </div>
                            </form>
                            <div class="mt-3" id="notification-result"></div>
                        </div>
                    </div>
                </div>

                <!-- 推送测试选项卡 -->
                <div class="tab-pane fade" id="test" role="tabpanel" aria-labelledby="test-tab">
                    <div class="card mb-4">
                        <div class="card-header pb-0">
                            <h6>推送服务测试</h6>
                        </div>
                        <div class="card-body">
                            <form id="test-form">
                                <div class="mb-3">
                                    <label for="test-title" class="form-label">
                                        <i class="bi bi-type me-1 text-primary"></i>测试标题
                                    </label>
                                    <input type="text" class="form-control" id="test-title" value="测试通知">
                                </div>
                                <div class="mb-3">
                                    <label for="test-message" class="form-label">
                                        <i class="bi bi-chat-text me-1 text-success"></i>测试内容
                                    </label>
                                    <textarea class="form-control" id="test-message" rows="3">这是一条测试通知，发送时间：{{ now }}</textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="test-tag" class="form-label">
                                        <i class="bi bi-tag me-1 text-warning"></i>标签（可选）
                                    </label>
                                    <input type="text" class="form-control" id="test-tag" placeholder="例如：test,all">
                                    <div class="form-text">用于筛选通知目标，多个标签用逗号分隔</div>
                                </div>
                                <div class="mb-3">
                                    <label for="test-url" class="form-label">
                                        <i class="bi bi-link me-1 text-danger"></i>特定URL（可选）
                                    </label>
                                    <input type="text" class="form-control" id="test-url" placeholder="例如：tgram://BOT_TOKEN/CHAT_ID">
                                    <div class="form-text">如果填写，将只发送到此URL，否则使用配置中的所有URL</div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="test-use-queue" checked>
                                        <label class="form-check-label" for="test-use-queue">使用推送队列</label>
                                    </div>
                                    <div class="form-text">启用后，测试消息将通过队列发送，否则直接发送</div>
                                </div>
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <button type="button" class="btn btn-primary" id="send-test-btn">
                                        <i class="bi bi-send me-1"></i>发送测试
                                    </button>
                                </div>
                            </form>
                            <div class="mt-3" id="test-result"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 清理模态框 -->
<div class="modal fade" id="cleanModal" tabindex="-1" aria-labelledby="cleanModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cleanModalLabel">清理通知记录</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="clean-status" class="form-label">状态</label>
                    <select class="form-select" id="clean-status">
                        <option value="all">全部</option>
                        <option value="success">成功</option>
                        <option value="failed">失败</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label for="clean-days" class="form-label">保留天数</label>
                    <input type="number" class="form-control" id="clean-days" value="30" min="1">
                    <div class="form-text">将删除超过指定天数的通知记录</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirm-clean-btn">确认清理</button>
            </div>
        </div>
    </div>
</div>

<!-- 通知详情模态框 -->
<div class="modal fade" id="detailModal" tabindex="-1" aria-labelledby="detailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="detailModalLabel">通知详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">标题</label>
                    <input type="text" class="form-control" id="detail-title" readonly>
                </div>
                <div class="mb-3">
                    <label class="form-label">内容</label>
                    <textarea class="form-control" id="detail-message" rows="5" readonly></textarea>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">状态</label>
                            <input type="text" class="form-control" id="detail-status" readonly>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">成功/总数</label>
                            <input type="text" class="form-control" id="detail-attempts" readonly>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">错误信息</label>
                    <textarea class="form-control" id="detail-error" rows="3" readonly></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="retry-btn" style="display: none;">重试</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/push_notifications.js') }}"></script>
{% endblock %}
