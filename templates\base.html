<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}TweetAnalyst - 社交媒体监控与分析助手{% endblock %}</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/theme.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dark-theme.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/components.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/nav-animations.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/global-animations.css') }}">
    <meta name="theme-color" content="#343a40">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    {% block head %}{% endblock %}
    {% block extra_css %}{% endblock %}
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="bi bi-twitter me-2"></i>TweetAnalyst
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                {% if session.get('user_id') %}
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('index') %}active{% endif %}" href="{{ url_for('index') }}">
                            <i class="bi bi-house-door me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('accounts') %}active{% endif %}" href="{{ url_for('accounts') }}">
                            <i class="bi bi-person-badge me-1"></i>账号管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('results') %}active{% endif %}" href="{{ url_for('results') }}">
                            <i class="bi bi-bar-chart me-1"></i>分析结果
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('analytics_page') %}active{% endif %}" href="{{ url_for('analytics_page') }}">
                            <i class="bi bi-graph-up me-1"></i>数据分析
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('config') %}active{% endif %}" href="{{ url_for('config') }}">
                            <i class="bi bi-gear me-1"></i>配置管理
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if 'settings' in request.path or 'test' in request.path or 'logs' in request.path %}active{% endif %}" href="#" id="settingsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-gear me-1"></i>系统设置
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="settingsDropdown">
                            <li><a class="dropdown-item" href="{{ url_for('unified_settings') }}"><i class="bi bi-gear-fill"></i> 统一设置中心</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('data_transfer') }}"><i class="bi bi-arrow-left-right"></i> 数据迁移</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('push_notifications.push_notifications_page') }}"><i class="bi bi-bell"></i> 推送通知管理</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('ai_settings.ai_settings_page') }}"><i class="bi bi-robot"></i> AI设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('system_status_page') }}"><i class="bi bi-activity"></i> 系统状态</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('logs_page') }}"><i class="bi bi-journal-text"></i> 系统日志</a></li>
                        </ul>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle position-relative" href="#" id="notificationsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-bell"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger d-none" id="notification-badge">
                                <span class="notification-count">0</span>
                                <span class="visually-hidden">未读通知</span>
                            </span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="notificationsDropdown" id="notifications-container" style="width: 300px; max-height: 400px; overflow-y: auto;">
                            <li><h6 class="dropdown-header">通知</h6></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-center" href="#">加载中...</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <div class="nav-link d-flex align-items-center">
                            <label class="theme-switch mb-0 me-2" title="切换主题">
                                <input type="checkbox" id="theme-toggle">
                                <span class="theme-switch-slider">
                                    <i class="bi bi-sun-fill theme-icon theme-icon-light"></i>
                                    <i class="bi bi-moon-fill theme-icon theme-icon-dark"></i>
                                </span>
                            </label>
                        </div>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('logout') }}">
                            <i class="bi bi-box-arrow-right me-1"></i>退出
                        </a>
                    </li>
                </ul>
                {% endif %}
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="ta-alert ta-alert-{{ category if category in ['success', 'warning', 'danger', 'info'] else 'info' }} alert-dismissible fade show">
                        {% if category == 'success' %}
                            <i class="bi bi-check-circle-fill me-2"></i>
                        {% elif category == 'warning' %}
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        {% elif category == 'danger' %}
                            <i class="bi bi-x-circle-fill me-2"></i>
                        {% else %}
                            <i class="bi bi-info-circle-fill me-2"></i>
                        {% endif %}
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <footer class="mt-5 py-3 bg-light">
        <div class="container text-center">
            <p class="text-muted">TweetAnalyst v0.10.0 - 社交媒体监控与分析助手</p>
        </div>
    </footer>

    <!-- 核心JavaScript库 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js" defer></script>
    <script src="{{ url_for('static', filename='js/components.js') }}" defer></script>
    <script src="{{ url_for('static', filename='js/main.js') }}" defer></script>

    <!-- API和通知模块 -->
    <script src="{{ url_for('static', filename='js/toast-notifications.js') }}" defer></script>
    <script src="{{ url_for('static', filename='js/fetch-helper.js') }}" defer></script>

    <!-- 自定义功能模块 -->
    <script src="{{ url_for('static', filename='js/csrf-protection.js') }}" defer></script>
    <script src="{{ url_for('static', filename='js/theme-switcher.js') }}" defer></script>
    {% if session.get('user_id') %}
    <script src="{{ url_for('static', filename='js/notifications.js') }}" defer></script>
    {% endif %}

    {% block scripts %}{% endblock %}

    <script>
        // 初始化通知组件
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化自定义alert的关闭按钮
            document.querySelectorAll('.ta-alert .btn-close').forEach(function(button) {
                button.addEventListener('click', function() {
                    // 获取父元素alert
                    const alert = this.closest('.ta-alert');
                    if (alert) {
                        // 手动移除alert
                        alert.classList.remove('show');
                        setTimeout(function() {
                            alert.remove();
                        }, 300);
                    }
                });
            });

            // 使用新的Toast通知系统处理闪现消息
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        {% if category == 'danger' or category == 'error' %}
                            TweetAnalyst.toast.showErrorToast("{{ message }}");
                        {% elif category == 'warning' %}
                            TweetAnalyst.toast.showWarningToast("{{ message }}");
                        {% elif category == 'success' %}
                            TweetAnalyst.toast.showSuccessToast("{{ message }}");
                        {% else %}
                            TweetAnalyst.toast.showInfoToast("{{ message }}");
                        {% endif %}
                    {% endfor %}
                {% endif %}
            {% endwith %}
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
