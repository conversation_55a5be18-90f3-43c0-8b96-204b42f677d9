/**
 * TweetAnalyst暗黑主题样式
 */

[data-theme="dark"] {
  /* 主色调 */
  --primary-color: #1DA1F2;
  --primary-dark: #0C85D0;
  --primary-light: #69C0FF;

  /* 辅助色 */
  --secondary-color: #8899A6;
  --secondary-dark: #657786;
  --secondary-light: #AAB8C2;

  /* 状态颜色 */
  --success-color: #4CAF50;
  --warning-color: #FFC107;
  --danger-color: #F44336;
  --info-color: #2196F3;

  /* 中性色 - 暗色模式（更柔和的颜色） */
  --gray-100: #2D3035;
  --gray-200: #383C44;
  --gray-300: #424652;
  --gray-400: #4D5160;
  --gray-500: #5F6577;
  --gray-600: #7D8599;
  --gray-700: #9BA3B9;
  --gray-800: #CDD3E5;
  --gray-900: #E9EDF5;

  /* 背景和文本颜色（更柔和的暗色） */
  --body-bg: #1E2029;
  --body-color: #FFFFFF;  /* 更亮的白色文本 */
  --card-bg: #2D3035;
  --card-border: #424652;
  --input-bg: #383C44;
  --input-border: #4D5160;
  --input-color: #FFFFFF;  /* 更亮的白色文本 */
  --navbar-bg: #1E2029;
  --navbar-color: #FFFFFF;  /* 更亮的白色文本 */
  --dropdown-bg: #2D3035;
  --dropdown-color: #FFFFFF;  /* 更亮的白色文本 */
  --dropdown-link-hover: #424652;
  --footer-bg: #2D3035;
  --footer-color: #CCCCCC;  /* 更亮的页脚文本 */
  --modal-bg: #2D3035;
  --modal-border: #424652;

  /* 阴影 */
  --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.2);
  --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.4);
}

/* 基础样式覆盖 */
[data-theme="dark"] body {
  background-color: var(--body-bg);
  color: var(--body-color);
}

/* 导航栏 */
[data-theme="dark"] .navbar-dark {
  background-color: var(--navbar-bg) !important;
}

[data-theme="dark"] .navbar-dark .navbar-brand,
[data-theme="dark"] .navbar-dark .nav-link {
  color: var(--navbar-color);
}

/* 下拉菜单 */
[data-theme="dark"] .dropdown-menu {
  background-color: var(--dropdown-bg);
  border-color: var(--card-border);
}

[data-theme="dark"] .dropdown-item {
  color: var(--dropdown-color);
}

[data-theme="dark"] .dropdown-item:hover,
[data-theme="dark"] .dropdown-item:focus {
  background-color: var(--dropdown-link-hover);
  color: var(--dropdown-color);
}

[data-theme="dark"] .dropdown-divider {
  border-top-color: var(--card-border);
}

/* 卡片 */
[data-theme="dark"] .card {
  background-color: var(--card-bg);
  border-color: var(--card-border);
}

[data-theme="dark"] .card-header,
[data-theme="dark"] .card-footer {
  background-color: rgba(0, 0, 0, 0.1);
  border-color: var(--card-border);
}

[data-theme="dark"] .card-header.bg-primary {
  background-color: var(--primary-color) !important;
}

[data-theme="dark"] .card-header.bg-success {
  background-color: var(--success-color) !important;
}

[data-theme="dark"] .card-header.bg-info {
  background-color: var(--info-color) !important;
}

[data-theme="dark"] .card-header.bg-warning {
  background-color: var(--warning-color) !important;
}

[data-theme="dark"] .card-header.bg-danger {
  background-color: var(--danger-color) !important;
}

[data-theme="dark"] .card-header.bg-light {
  background-color: var(--gray-200) !important;
  color: var(--body-color);
}

/* 表单控件 */
[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
  background-color: var(--input-bg);
  border-color: var(--input-border);
  color: var(--input-color);
}

[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.25rem rgba(29, 161, 242, 0.25);
}

[data-theme="dark"] .form-text {
  color: var(--gray-700);
}

[data-theme="dark"] .form-check-input {
  background-color: var(--input-bg);
  border-color: var(--input-border);
}

/* 表格 */
[data-theme="dark"] .table {
  color: var(--body-color);
  background-color: var(--card-bg);
}

[data-theme="dark"] .table-striped > tbody > tr:nth-of-type(odd) > * {
  background-color: rgba(0, 0, 0, 0.1);
  color: var(--body-color);
}

[data-theme="dark"] .table-hover > tbody > tr:hover > * {
  background-color: rgba(0, 0, 0, 0.2);
  color: var(--body-color);
}

[data-theme="dark"] .table-bordered,
[data-theme="dark"] .table-bordered th,
[data-theme="dark"] .table-bordered td {
  border-color: var(--card-border);
}

/* 表格行和单元格 */
[data-theme="dark"] .table > :not(caption) > * > * {
  background-color: var(--card-bg);
  color: #FFFFFF;  /* 纯白色文本 */
  border-bottom-color: var(--card-border);
}

/* 表格响应式容器 */
[data-theme="dark"] .table-responsive {
  background-color: var(--card-bg);
}

/* 确保表格在暗黑模式下正确显示 */
[data-theme="dark"] table,
[data-theme="dark"] .table,
[data-theme="dark"] .table tbody,
[data-theme="dark"] .table thead,
[data-theme="dark"] .table tfoot,
[data-theme="dark"] .table tr,
[data-theme="dark"] .table th,
[data-theme="dark"] .table td {
  background-color: var(--card-bg);
  color: #FFFFFF;  /* 纯白色文本 */
}

/* 表格头部和底部 */
[data-theme="dark"] .table thead,
[data-theme="dark"] .table tfoot {
  background-color: var(--gray-300);
  color: #FFFFFF;  /* 纯白色文本 */
}

/* 表格头部单元格 */
[data-theme="dark"] .table thead th,
[data-theme="dark"] .table thead td {
  background-color: var(--gray-300);
  color: #FFFFFF;  /* 纯白色文本 */
  border-bottom-color: var(--card-border);
}

[data-theme="dark"] .table-light,
[data-theme="dark"] .table-light > th,
[data-theme="dark"] .table-light > td,
[data-theme="dark"] .table > thead.table-light > tr > th,
[data-theme="dark"] .table > tbody.table-light > tr > th,
[data-theme="dark"] .table > tfoot.table-light > tr > th,
[data-theme="dark"] .table > thead > tr.table-light > th,
[data-theme="dark"] .table > tbody > tr.table-light > th,
[data-theme="dark"] .table > tfoot > tr.table-light > th,
[data-theme="dark"] .table > thead.table-light > tr > td,
[data-theme="dark"] .table > tbody.table-light > tr > td,
[data-theme="dark"] .table > tfoot.table-light > tr > td,
[data-theme="dark"] .table > thead > tr.table-light > td,
[data-theme="dark"] .table > tbody > tr.table-light > td,
[data-theme="dark"] .table > tfoot > tr.table-light > td {
  background-color: var(--gray-300);
  color: #FFFFFF;  /* 纯白色文本 */
}

/* 表格表头 */
[data-theme="dark"] thead.table-light,
[data-theme="dark"] .table > thead.table-light > tr,
[data-theme="dark"] .table > thead > tr.table-light {
  background-color: var(--gray-300);
  color: #FFFFFF;  /* 纯白色文本 */
}

/* 按钮 */
[data-theme="dark"] .btn-light {
  background-color: var(--gray-300);
  border-color: var(--gray-400);
  color: var(--body-color);
}

[data-theme="dark"] .btn-light:hover {
  background-color: var(--gray-400);
  border-color: var(--gray-500);
  color: var(--body-color);
}

[data-theme="dark"] .btn-outline-primary,
[data-theme="dark"] .btn-outline-secondary,
[data-theme="dark"] .btn-outline-success,
[data-theme="dark"] .btn-outline-info,
[data-theme="dark"] .btn-outline-warning,
[data-theme="dark"] .btn-outline-danger {
  color: var(--body-color);
}

/* 徽章 */
[data-theme="dark"] .badge.bg-light {
  background-color: var(--gray-300) !important;
  color: var(--gray-900) !important;
}

[data-theme="dark"] .badge.text-dark {
  color: var(--gray-900) !important;
}

/* 模态框 */
[data-theme="dark"] .modal-content {
  background-color: var(--modal-bg);
  border-color: var(--modal-border);
}

[data-theme="dark"] .modal-header,
[data-theme="dark"] .modal-footer {
  border-color: var(--modal-border);
}

/* 列表组 */
[data-theme="dark"] .list-group-item {
  background-color: var(--card-bg);
  border-color: var(--card-border);
  color: #FFFFFF;  /* 纯白色文本 */
}

[data-theme="dark"] .list-group-item-action:hover {
  background-color: var(--gray-300);
}

/* 页脚 */
[data-theme="dark"] footer.bg-light {
  background-color: var(--footer-bg) !important;
}

[data-theme="dark"] footer .text-muted {
  color: var(--footer-color) !important;
}

/* 警告框 */
[data-theme="dark"] .ta-alert-success {
  background-color: rgba(76, 175, 80, 0.1);
  border-color: rgba(76, 175, 80, 0.2);
  color: #81c784;
}

[data-theme="dark"] .ta-alert-warning {
  background-color: rgba(255, 193, 7, 0.1);
  border-color: rgba(255, 193, 7, 0.2);
  color: #ffd54f;
}

[data-theme="dark"] .ta-alert-danger {
  background-color: rgba(244, 67, 54, 0.1);
  border-color: rgba(244, 67, 54, 0.2);
  color: #e57373;
}

[data-theme="dark"] .ta-alert-info {
  background-color: rgba(33, 150, 243, 0.1);
  border-color: rgba(33, 150, 243, 0.2);
  color: #64b5f6;
}

/* 代码编辑器 */
[data-theme="dark"] .CodeMirror {
  background-color: var(--input-bg);
  color: var(--input-color);
  border-color: var(--input-border);
}

/* 文本颜色 */
[data-theme="dark"] .text-muted {
  color: #BBBBBB !important;  /* 更亮的灰色文本 */
}

[data-theme="dark"] .text-dark {
  color: var(--body-color) !important;
}

/* 背景颜色 */
[data-theme="dark"] .bg-light {
  background-color: var(--gray-200) !important;
}

[data-theme="dark"] .bg-white {
  background-color: var(--card-bg) !important;
}

/* 边框颜色 */
[data-theme="dark"] .border,
[data-theme="dark"] .border-top,
[data-theme="dark"] .border-end,
[data-theme="dark"] .border-bottom,
[data-theme="dark"] .border-start {
  border-color: var(--card-border) !important;
}

/* 链接颜色 */
[data-theme="dark"] a {
  color: var(--primary-light);
}

[data-theme="dark"] a:hover {
  color: var(--primary-color);
}

/* 输入组 */
[data-theme="dark"] .input-group-text {
  background-color: var(--gray-300);
  border-color: var(--input-border);
  color: var(--body-color);
}

/* 特定页面样式 */

/* 首页 */
[data-theme="dark"] .dashboard-stat {
  background-color: var(--card-bg);
  border-color: var(--card-border);
}

[data-theme="dark"] .dashboard-stat .stat-icon {
  color: var(--primary-color);
}

[data-theme="dark"] .dashboard-stat .stat-value {
  color: var(--body-color);
}

[data-theme="dark"] .dashboard-stat .stat-label {
  color: var(--gray-700);
}

/* 账号页面 */
[data-theme="dark"] .account-card {
  background-color: var(--card-bg);
  border-color: var(--card-border);
}

[data-theme="dark"] .account-sidebar {
  background-color: var(--card-bg);
  border-color: var(--card-border);
}

/* 配置页面 */
[data-theme="dark"] .config-container {
  background-color: var(--card-bg);
}

[data-theme="dark"] .editor-toolbar {
  background-color: var(--gray-200);
  border-color: var(--card-border);
}

[data-theme="dark"] .config-status {
  color: var(--body-color);
}

/* 分析结果页面 */
[data-theme="dark"] .result-card {
  background-color: var(--card-bg);
  border-color: var(--card-border);
}

[data-theme="dark"] .result-meta {
  color: var(--gray-700);
}

[data-theme="dark"] .result-content {
  color: var(--body-color);
}

[data-theme="dark"] .result-actions {
  background-color: var(--gray-200);
}

/* 分析结果表格 */
[data-theme="dark"] #table-view {
  background-color: var(--card-bg);
}

[data-theme="dark"] .result-row {
  background-color: var(--card-bg);
}

[data-theme="dark"] .result-row:hover {
  background-color: var(--gray-200);
}

[data-theme="dark"] .result-row td {
  color: #FFFFFF;  /* 纯白色文本 */
  border-color: var(--card-border);
}

/* 分析结果详情模态框 */
[data-theme="dark"] #modal-content {
  background-color: var(--gray-200) !important;
  color: #FFFFFF;  /* 纯白色文本 */
}

[data-theme="dark"] #modal-analysis {
  background-color: var(--card-bg);
  color: #FFFFFF;  /* 纯白色文本 */
}

[data-theme="dark"] #modal-reason {
  background-color: var(--gray-200) !important;
  color: #FFFFFF;  /* 纯白色文本 */
}

/* 测试页面 */
[data-theme="dark"] .test-section {
  background-color: var(--card-bg);
  border-color: var(--card-border);
  color: #FFFFFF;  /* 纯白色文本 */
}

[data-theme="dark"] .test-result {
  background-color: var(--gray-200);
}

[data-theme="dark"] .test-success {
  color: var(--success-color);
}

[data-theme="dark"] .test-failure {
  color: var(--danger-color);
}

/* 日志页面 */
[data-theme="dark"] .log-entry {
  border-color: var(--card-border);
}

[data-theme="dark"] .log-entry:hover {
  background-color: var(--gray-200);
}

[data-theme="dark"] .log-level-info {
  color: var(--info-color);
}

[data-theme="dark"] .log-level-warning {
  color: var(--warning-color);
}

[data-theme="dark"] .log-level-error {
  color: var(--danger-color);
}

[data-theme="dark"] .log-timestamp {
  color: #BBBBBB;  /* 更亮的灰色文本 */
}

/* 导入/导出页面 */
[data-theme="dark"] .import-dropzone {
  background-color: var(--gray-200);
  border-color: var(--card-border);
}

[data-theme="dark"] .import-dropzone.dragover {
  background-color: var(--gray-300);
  border-color: var(--primary-color);
}

/* 设置页面 */
[data-theme="dark"] .settings-section {
  background-color: var(--card-bg);
  border-color: var(--card-border);
}

[data-theme="dark"] .settings-label {
  color: #FFFFFF;  /* 纯白色文本 */
}

[data-theme="dark"] .settings-description {
  color: #BBBBBB;  /* 更亮的灰色文本 */
}

/* 主题切换按钮 */
.theme-switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 30px;
  margin: 0;
  vertical-align: middle;
}

.theme-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.theme-switch-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 30px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
}

.theme-switch-slider:before {
  position: absolute;
  content: "";
  height: 22px;
  width: 22px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

input:checked + .theme-switch-slider {
  background-color: var(--primary-color);
}

input:focus + .theme-switch-slider {
  box-shadow: 0 0 1px var(--primary-color);
}

input:checked + .theme-switch-slider:before {
  transform: translateX(30px);
}

.theme-switch-slider .theme-icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  transition: .4s;
}

.theme-switch-slider .theme-icon-light {
  left: 8px;
  color: #fff;
  opacity: 0;
}

.theme-switch-slider .theme-icon-dark {
  right: 8px;
  color: #333;
}

input:checked + .theme-switch-slider .theme-icon-light {
  opacity: 1;
}

input:checked + .theme-switch-slider .theme-icon-dark {
  opacity: 0;
}

/* 移动设备上的主题切换按钮 */
@media (max-width: 768px) {
  .theme-switch {
    width: 50px;
    height: 26px;
  }

  .theme-switch-slider:before {
    height: 18px;
    width: 18px;
  }

  input:checked + .theme-switch-slider:before {
    transform: translateX(24px);
  }

  .theme-switch-slider .theme-icon {
    font-size: 10px;
  }
}
