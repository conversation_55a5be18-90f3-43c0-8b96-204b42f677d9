/**
 * TweetAnalyst UI组件样式
 * 配合components.js使用
 */

/* 通知组件 */
.ta-notification-container {
  position: fixed;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  max-width: 350px;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
}

.ta-notification-top-right {
  top: 0;
  right: 0;
}

.ta-notification-top-left {
  top: 0;
  left: 0;
}

.ta-notification-bottom-right {
  bottom: 0;
  right: 0;
}

.ta-notification-bottom-left {
  bottom: 0;
  left: 0;
}

.ta-notification {
  position: relative;
  background-color: #fff;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-md);
  padding: 15px;
  margin-bottom: 10px;
  opacity: 0;
  transform: translateX(40px);
  transition: all 0.3s ease-in-out;
  overflow: hidden;
}

.ta-notification-show {
  opacity: 1;
  transform: translateX(0);
}

.ta-notification-hide {
  opacity: 0;
  transform: translateX(40px);
}

.ta-notification-close {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 1.5rem;
  line-height: 1;
  cursor: pointer;
  color: var(--gray-600);
  padding: 0;
  margin: 0;
}

.ta-notification-title {
  margin: 0 0 5px 0;
  font-size: var(--font-size-lg);
  font-weight: 600;
  padding-right: 20px;
}

.ta-notification-message {
  margin: 0;
  font-size: var(--font-size-md);
}

.ta-notification-success {
  border-left: 4px solid var(--success-color);
}

.ta-notification-success .ta-notification-title {
  color: var(--success-color);
}

.ta-notification-danger {
  border-left: 4px solid var(--danger-color);
}

.ta-notification-danger .ta-notification-title {
  color: var(--danger-color);
}

.ta-notification-warning {
  border-left: 4px solid var(--warning-color);
}

.ta-notification-warning .ta-notification-title {
  color: var(--warning-color);
}

.ta-notification-info {
  border-left: 4px solid var(--info-color);
}

.ta-notification-info .ta-notification-title {
  color: var(--info-color);
}

/* 确认对话框组件 */
.ta-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease-in-out;
}

.ta-modal-show {
  opacity: 1;
  visibility: visible;
}

.ta-modal-dialog {
  width: 100%;
  max-width: 500px;
  margin: 1.75rem auto;
  transform: translateY(-50px);
  transition: transform 0.3s ease-in-out;
}

.ta-modal-show .ta-modal-dialog {
  transform: translateY(0);
}

.ta-modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  background-color: #fff;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-lg);
  outline: 0;
}

.ta-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid var(--gray-200);
  border-top-left-radius: var(--border-radius-md);
  border-top-right-radius: var(--border-radius-md);
}

.ta-modal-title {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: 600;
  line-height: 1.5;
}

.ta-modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  line-height: 1;
  cursor: pointer;
  color: var(--gray-600);
  padding: 0;
  margin: 0;
}

.ta-modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: 1rem;
}

.ta-modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 1rem;
  border-top: 1px solid var(--gray-200);
  border-bottom-left-radius: var(--border-radius-md);
  border-bottom-right-radius: var(--border-radius-md);
}

.ta-modal-footer > .ta-btn {
  margin-left: 0.5rem;
}

/* 加载器组件 */
.ta-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease-in-out;
}

.ta-loader-show {
  opacity: 1;
  visibility: visible;
}

.ta-loader-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.ta-loader-content {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: #fff;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-lg);
}

.ta-loader-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid var(--gray-200);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: ta-spinner 1s linear infinite;
}

.ta-loader-text {
  margin-top: 1rem;
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--gray-700);
}

@keyframes ta-spinner {
  to {
    transform: rotate(360deg);
  }
}

/* 数据表格组件 */
.ta-table {
  width: 100%;
  margin-bottom: 1rem;
  color: var(--gray-800);
  border-collapse: collapse;
}

.ta-table th,
.ta-table td {
  padding: 0.75rem;
  vertical-align: top;
  border-top: 1px solid var(--gray-300);
}

.ta-table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid var(--gray-300);
  background-color: var(--gray-100);
  font-weight: 600;
  text-align: left;
}

.ta-table tbody + tbody {
  border-top: 2px solid var(--gray-300);
}

.ta-table-sm th,
.ta-table-sm td {
  padding: 0.3rem;
}

.ta-table-bordered {
  border: 1px solid var(--gray-300);
}

.ta-table-bordered th,
.ta-table-bordered td {
  border: 1px solid var(--gray-300);
}

.ta-table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.05);
}

.ta-table-hover tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.075);
}

/* 标签页组件 */
.ta-tabs {
  display: flex;
  flex-direction: column;
}

.ta-tabs-nav {
  display: flex;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
  border-bottom: 1px solid var(--gray-300);
}

.ta-tabs-link {
  display: block;
  padding: 0.5rem 1rem;
  margin-bottom: -1px;
  border: 1px solid transparent;
  border-top-left-radius: var(--border-radius-sm);
  border-top-right-radius: var(--border-radius-sm);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.ta-tabs-link:hover {
  border-color: var(--gray-200) var(--gray-200) var(--gray-300);
}

.ta-tabs-link.active {
  color: var(--primary-color);
  background-color: #fff;
  border-color: var(--gray-300) var(--gray-300) #fff;
  font-weight: 500;
}

.ta-tabs-content {
  padding: 1rem 0;
}

.ta-tabs-pane {
  display: none;
}

.ta-tabs-pane.active {
  display: block;
}
