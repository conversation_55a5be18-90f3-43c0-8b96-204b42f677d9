/**
 * TweetAnalyst主CSS文件
 * 导入所有CSS文件，优化加载顺序
 */

/* 基础样式 */
@import url('theme.css');
@import url('style.css');

/* 组件样式 */
@import url('components.css');
@import url('table-component.css');
@import url('form-validator.css');

/* 动画和效果 */
@import url('global-animations.css');
@import url('nav-animations.css');

/* 主题 */
@import url('dark-theme.css');

/* 关键CSS（始终保持在最后，以确保优先级） */
.ta-critical {
  display: block !important;
}

.ta-hidden {
  display: none !important;
}

.ta-invisible {
  visibility: hidden !important;
}

.ta-visible {
  visibility: visible !important;
}

/* 响应式工具类 */
@media (max-width: 576px) {
  .ta-hide-xs {
    display: none !important;
  }
}

@media (min-width: 576px) and (max-width: 768px) {
  .ta-hide-sm {
    display: none !important;
  }
}

@media (min-width: 768px) and (max-width: 992px) {
  .ta-hide-md {
    display: none !important;
  }
}

@media (min-width: 992px) {
  .ta-hide-lg {
    display: none !important;
  }
}

/* 打印样式 */
@media print {
  .ta-no-print {
    display: none !important;
  }
  
  .ta-print-only {
    display: block !important;
  }
  
  a[href]:after {
    content: " (" attr(href) ")";
  }
  
  abbr[title]:after {
    content: " (" attr(title) ")";
  }
  
  .ta-table {
    border-collapse: collapse !important;
  }
  
  .ta-table td,
  .ta-table th {
    background-color: #fff !important;
  }
}
