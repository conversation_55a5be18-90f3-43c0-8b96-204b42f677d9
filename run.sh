#!/bin/bash

# TweetAnalyst 应用启动脚本
# 用于在Linux/Unix系统上启动应用

set -e

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}===== TweetAnalyst 应用启动脚本 =====${NC}"

# 检查Python版本
echo -e "${BLUE}检查Python版本...${NC}"
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}错误: 未找到Python3${NC}"
    exit 1
fi

PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
echo -e "${GREEN}Python版本: $PYTHON_VERSION${NC}"

# 检查是否满足最低版本要求
if python3 -c 'import sys; exit(0 if sys.version_info >= (3, 8) else 1)'; then
    echo -e "${GREEN}Python版本满足要求 (>= 3.8)${NC}"
else
    echo -e "${RED}错误: Python版本过低，需要3.8或更高版本${NC}"
    exit 1
fi

# 检查虚拟环境
if [[ "$VIRTUAL_ENV" != "" ]]; then
    echo -e "${GREEN}检测到虚拟环境: $VIRTUAL_ENV${NC}"
else
    echo -e "${YELLOW}警告: 未检测到虚拟环境，建议使用虚拟环境${NC}"
fi

# 检查依赖
echo -e "${BLUE}检查依赖...${NC}"
if [ -f requirements.txt ]; then
    echo -e "${GREEN}找到requirements.txt文件${NC}"
    
    # 检查关键依赖是否已安装
    MISSING_DEPS=()
    
    if ! python3 -c "import flask" 2>/dev/null; then
        MISSING_DEPS+=("Flask")
    fi
    
    if ! python3 -c "import requests" 2>/dev/null; then
        MISSING_DEPS+=("requests")
    fi
    
    if ! python3 -c "import schedule" 2>/dev/null; then
        MISSING_DEPS+=("schedule")
    fi
    
    if [ ${#MISSING_DEPS[@]} -gt 0 ]; then
        echo -e "${YELLOW}检测到缺失的依赖: ${MISSING_DEPS[*]}${NC}"
        read -p "是否自动安装依赖？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            echo -e "${BLUE}安装依赖...${NC}"
            pip3 install -r requirements.txt
        else
            echo -e "${RED}请手动安装依赖: pip3 install -r requirements.txt${NC}"
            exit 1
        fi
    else
        echo -e "${GREEN}所有关键依赖已安装${NC}"
    fi
else
    echo -e "${YELLOW}警告: 未找到requirements.txt文件${NC}"
fi

# 检查配置文件
echo -e "${BLUE}检查配置...${NC}"
if [ -f .env ]; then
    echo -e "${GREEN}找到.env配置文件${NC}"
else
    echo -e "${YELLOW}未找到.env文件${NC}"
    if [ -f example.env ]; then
        echo -e "${YELLOW}发现example.env文件${NC}"
        read -p "是否复制example.env为.env？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            cp example.env .env
            echo -e "${GREEN}已创建.env文件，请编辑配置${NC}"
        fi
    fi
fi

# 创建必要的目录
echo -e "${BLUE}创建必要的目录...${NC}"
mkdir -p instance
mkdir -p logs
mkdir -p data

# 设置权限
chmod +x run_all.py 2>/dev/null || true

# 启动应用
echo -e "${GREEN}启动TweetAnalyst应用...${NC}"
echo -e "${BLUE}访问地址: http://localhost:5000${NC}"
echo -e "${YELLOW}按 Ctrl+C 停止应用${NC}"
echo ""

# 启动应用
python3 run_all.py

--------原始代码----
#!/bin/bash

# 设置脚本所在目录为工作目录
cd "$(dirname "$0")"

uv run python main.py
