#!/bin/bash
# TweetAnalyst Docker部署修复脚本

echo "🔧 开始修复Docker部署问题..."

# 1. 停止并清理现有容器
echo "1. 清理现有容器和网络..."
docker-compose down 2>/dev/null || true
docker system prune -f
docker network prune -f

# 2. 修复文件权限
echo "2. 修复文件权限..."
chmod +x docker-entrypoint.sh
chmod 644 docker-compose.yml
chmod 644 Dockerfile
chmod 644 requirements.txt

# 3. 检查并修复docker-compose.yml
echo "3. 检查docker-compose.yml配置..."
if grep -q "your_dockerhub_username" docker-compose.yml; then
    echo "⚠️ 检测到占位符用户名，请手动修改docker-compose.yml中的镜像名称"
    echo "   将 'your_dockerhub_username/tweetanalyst:latest' 替换为实际的镜像名称"
fi

# 4. 验证配置
echo "4. 验证配置..."
docker-compose config

# 5. 重新构建并启动
echo "5. 重新构建并启动..."
docker-compose up --build -d

echo "✅ 修复完成！"
echo "📝 请查看日志: docker-compose logs -f"
